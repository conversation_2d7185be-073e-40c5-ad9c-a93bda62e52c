#include "bsp_timer.h"

static u16 stop_time = 0;//延迟时间  delay time
static u32 tim1_counter = 0;//定时器1计数器  TIM1 counter

/**************************************************************************
Function function: TIM1 initialization, timed for 1 millisecond
Entrance parameters: None
Return value: None
功能函数：TIM1初始化，定时1毫秒
入口参数：无
返回  值：无
**************************************************************************/
void TIM1_Init(void)
{
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	NVIC_InitTypeDef NVIC_InitStructure;

	// 使能定时器1时钟  Enable TIM1 clock
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1, ENABLE);

	// 定时器1配置  TIM1 configuration
	// 假设系统时钟为72MHz，APB2时钟为72MHz
	// 预分频器设置为71，则定时器时钟为72MHz/(71+1) = 1MHz
	// 自动重装载值设置为999，则定时周期为(999+1)/1MHz = 1ms
	TIM_TimeBaseStructure.TIM_Prescaler = 71;			// 预分频器  Prescaler (72MHz/72 = 1MHz)
	TIM_TimeBaseStructure.TIM_Period = 999;				// 自动重装载值  Auto-reload value (1000/1MHz = 1ms)
	TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;		// 时钟分频  Clock division
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;	// 向上计数模式  Up counting mode
	TIM_TimeBaseStructure.TIM_RepetitionCounter = 0;			// 重复计数器  Repetition counter
	TIM_TimeBaseInit(TIM1, &TIM_TimeBaseStructure);

	// 清除定时器1更新标志位  Clear TIM1 update flag
	TIM_ClearFlag(TIM1, TIM_FLAG_Update);

	// 使能定时器1更新中断  Enable TIM1 update interrupt
	TIM_ITConfig(TIM1, TIM_IT_Update, ENABLE);

	// 中断优先级NVIC设置  NVIC configuration
	NVIC_InitStructure.NVIC_IRQChannel = TIM1_UP_IRQn;			// TIM1更新中断  TIM1 update interrupt
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;	// 抢占优先级2级  Preemption priority level 2
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;			// 从优先级1级  Sub priority level 1
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;				// IRQ通道被使能  IRQ channel is enabled
	NVIC_Init(&NVIC_InitStructure);								// 初始化NVIC寄存器  Initialize NVIC registers

	// 使能定时器1  Enable TIM1
	TIM_Cmd(TIM1, ENABLE);
}

//定时器6的延迟 10ms的延迟 此方法比delay准确
//Timer 6 has a delay of 10ms. This method is more accurate than delay
void delay_time(u16 time)
{
	stop_time = time;
	while(stop_time);//等待 Wait
}

//延迟1s  Unit second
void my_delay(u16 s)//s
{
	for(int i = 0;i<s;i++)
	{
		delay_time(100);
	}
}


/**************************************************************************
Function function: TIM3 initialization, timed for 20 milliseconds
Entrance parameters: None
Return value: None
功能函数：TIM3初始化，定时20毫秒
入口参数：无
返回  值：无
**************************************************************************/
void TIM3_Init(void)
{
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE); //使能定时器时钟  Enable the clock of the timer
	TIM_TimeBaseStructure.TIM_Prescaler = 7199;			 // 预分频器  Prescaler
	TIM_TimeBaseStructure.TIM_Period = 99;				 //设定计数器自动重装值  Set the automatic reset value of the counter
	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseStructure);
	TIM_ClearFlag(TIM3, TIM_FLAG_Update);                //清除TIM的更新标志位 Clear the update flag of TIM
	TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE);

	//中断优先级NVIC设置
	NVIC_InitStructure.NVIC_IRQChannel = TIM3_IRQn;			  //TIM3中断	TIM3 interrupt
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 4; //抢占优先级4级	Preempts priority level 4
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;		  //从优先级2级	From priority level 2
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;			  //IRQ通道被使能	IRQ channel is enabled
	NVIC_Init(&NVIC_InitStructure);							  //初始化NVIC寄存器	Initializes NVIC registers

	TIM_Cmd(TIM3, ENABLE);
}


// TIM3中断 //TIM3 Interrupt service
void TIM3_IRQHandler(void)
{
	if (TIM_GetITStatus(TIM3, TIM_IT_Update) != RESET) //检查TIM更新中断发生与否	Check whether TIM update interrupt occurs
	{
		TIM_ClearITPendingBit(TIM3, TIM_IT_Update);    //清除TIMx更新中断标志	Clear TIMx update interrupt flag

		times++;
		
		if(stop_time>0)
		{
			stop_time --;
		}
	}
}


// 获取定时器1计数器值的函数  Function to get TIM1 counter value
u32 TIM1_GetCounter(void)
{
	return tim1_counter;
}

// 重置定时器1计数器的函数  Function to reset TIM1 counter
void TIM1_ResetCounter(void)
{
	tim1_counter = 0;
}

