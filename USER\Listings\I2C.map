Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.AngleDifference) refers to fadd.o(.text) for __aeabi_fsub
    main.o(i.AngleDifference) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    main.o(i.AngleDifference) refers to cfcmple.o(.text) for __aeabi_cfcmple
    main.o(i.Car_Move) refers to bsp_motor_iic.o(i.control_speed) for control_speed
    main.o(i.Car_Move) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Car_Move) refers to main.o(i.StartRotation) for StartRotation
    main.o(i.Car_Move) refers to main.o(.data) for state
    main.o(i.GetRotationState) refers to main.o(.data) for rotation_state
    main.o(i.IsRotationComplete) refers to main.o(.data) for rotation_state
    main.o(i.PID_Calculate) refers to fadd.o(.text) for __aeabi_fsub
    main.o(i.PID_Calculate) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.PID_Calculate) refers to cdcmple.o(.text) for __aeabi_cdcmple
    main.o(i.PID_Calculate) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    main.o(i.PID_Calculate) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.PID_Calculate) refers to cfcmple.o(.text) for __aeabi_cfcmple
    main.o(i.PID_Calculate) refers to main.o(.data) for last_derivative
    main.o(i.ProcessRotationControl) refers to bsp_motor_iic.o(i.control_speed) for control_speed
    main.o(i.ProcessRotationControl) refers to main.o(i.AngleDifference) for AngleDifference
    main.o(i.ProcessRotationControl) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.ProcessRotationControl) refers to cdcmple.o(.text) for __aeabi_cdcmple
    main.o(i.ProcessRotationControl) refers to main.o(i.PID_Calculate) for PID_Calculate
    main.o(i.ProcessRotationControl) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    main.o(i.ProcessRotationControl) refers to fadd.o(.text) for __aeabi_fsub
    main.o(i.ProcessRotationControl) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.ProcessRotationControl) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(i.ProcessRotationControl) refers to dflti.o(.text) for __aeabi_i2d
    main.o(i.ProcessRotationControl) refers to main.o(.data) for rotation_state
    main.o(i.ProcessRotationControl) refers to main.o(.bss) for yaw_pid
    main.o(i.StartRotation) refers to bsp_motor_iic.o(i.control_speed) for control_speed
    main.o(i.StartRotation) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.StartRotation) refers to inv_mpu.o(i.MPU6050_DMP_Get_Data) for MPU6050_DMP_Get_Data
    main.o(i.StartRotation) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.StartRotation) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    main.o(i.StartRotation) refers to cfcmple.o(.text) for __aeabi_cfcmple
    main.o(i.StartRotation) refers to main.o(.data) for rotation_state
    main.o(i.StartRotation) refers to main.o(.bss) for yaw_pid
    main.o(i.StartRotationAngle) refers to fadd.o(.text) for __aeabi_fadd
    main.o(i.StartRotationAngle) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    main.o(i.StartRotationAngle) refers to cfcmple.o(.text) for __aeabi_cfcmple
    main.o(i.StartRotationAngle) refers to main.o(.data) for rotation_state
    main.o(i.StartRotationAngle) refers to main.o(.bss) for yaw_pid
    main.o(i.StopRotation) refers to bsp_motor_iic.o(i.control_speed) for control_speed
    main.o(i.StopRotation) refers to main.o(.data) for rotation_state
    main.o(i.TIM1_UP_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM1_UP_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.TIM1_UP_IRQHandler) refers to main.o(i.ProcessRotationControl) for ProcessRotationControl
    main.o(i.TIM1_UP_IRQHandler) refers to main.o(.data) for tim1_counter
    main.o(i.TestRotation) refers to bsp_timer.o(i.TIM1_GetCounter) for TIM1_GetCounter
    main.o(i.TestRotation) refers to main.o(i.StartRotation) for StartRotation
    main.o(i.TestRotation) refers to main.o(i.IsRotationComplete) for IsRotationComplete
    main.o(i.TestRotation) refers to main.o(.data) for test_state
    main.o(i.main) refers to bsp.o(i.bsp_init) for bsp_init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to mpu6050.o(i.MPU6050_Init) for MPU6050_Init
    main.o(i.main) refers to inv_mpu.o(i.MPU6050_DMP_Init) for MPU6050_DMP_Init
    main.o(i.main) refers to bsp_timer.o(i.TIM1_Init) for TIM1_Init
    main.o(i.main) refers to bsp_timer.o(i.TIM3_Init) for TIM3_Init
    main.o(i.main) refers to bsp_motor_iic.o(i.IIC_Motor_Init) for IIC_Motor_Init
    main.o(i.main) refers to bsp_motor_iic.o(i.control_pwm) for control_pwm
    main.o(i.main) refers to main.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to bsp_motor_iic.o(i.Set_motor_type) for Set_motor_type
    main.o(i.main) refers to bsp_motor_iic.o(i.Set_Pluse_Phase) for Set_Pluse_Phase
    main.o(i.main) refers to bsp_motor_iic.o(i.Set_Pluse_line) for Set_Pluse_line
    main.o(i.main) refers to bsp_motor_iic.o(i.Set_Wheel_dis) for Set_Wheel_dis
    main.o(i.main) refers to bsp_motor_iic.o(i.Set_motor_deadzone) for Set_motor_deadzone
    main.o(i.main) refers to main.o(i.StartRotation) for StartRotation
    main.o(i.main) refers to inv_mpu.o(i.MPU6050_DMP_Get_Data) for MPU6050_DMP_Get_Data
    main.o(i.main) refers to mpu6050.o(i.MPU_Get_Gyroscope) for MPU_Get_Gyroscope
    main.o(i.main) refers to mpu6050.o(i.MPU_Get_Accelerometer) for MPU_Get_Accelerometer
    main.o(i.main) refers to ffixi.o(.text) for __aeabi_f2iz
    main.o(i.main) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(i.main) refers to main.o(i.AngleDifference) for AngleDifference
    main.o(i.main) refers to main.o(i.Car_Move) for Car_Move
    main.o(i.main) refers to main.o(i.TestRotation) for TestRotation
    main.o(i.main) refers to main.o(.bss) for yaw_pid
    main.o(i.main) refers to main.o(.data) for Yaw
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to bsp_timer.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    bsp.o(i.bsp_init) refers to bsp.o(i.DIY_NVIC_PriorityGroupConfig) for DIY_NVIC_PriorityGroupConfig
    bsp.o(i.bsp_init) refers to delay.o(i.delay_init) for delay_init
    bsp.o(i.bsp_init) refers to bsp.o(i.JTAG_Set) for JTAG_Set
    bsp.o(i.bsp_init) refers to usart.o(i.uart_init) for uart_init
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(i.USART1_Send_U8) for USART1_Send_U8
    usart.o(i.USART1_Send_ArrayU8) refers to usart.o(i.USART1_Send_U8) for USART1_Send_U8
    usart.o(i.USART1_Send_U8) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_Send_U8) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.uart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_timer.o(i.TIM1_GetCounter) refers to bsp_timer.o(.data) for tim1_counter
    bsp_timer.o(i.TIM1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_timer.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    bsp_timer.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    bsp_timer.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    bsp_timer.o(i.TIM1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_timer.o(i.TIM1_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_timer.o(i.TIM1_ResetCounter) refers to bsp_timer.o(.data) for tim1_counter
    bsp_timer.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    bsp_timer.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    bsp_timer.o(i.TIM3_IRQHandler) refers to main.o(.data) for times
    bsp_timer.o(i.TIM3_IRQHandler) refers to bsp_timer.o(.data) for stop_time
    bsp_timer.o(i.TIM3_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    bsp_timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    bsp_timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    bsp_timer.o(i.TIM3_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_timer.o(i.delay_time) refers to bsp_timer.o(.data) for stop_time
    bsp_timer.o(i.my_delay) refers to bsp_timer.o(i.delay_time) for delay_time
    bsp_motor_iic.o(i.IIC_Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_motor_iic.o(i.IIC_Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_motor_iic.o(i.Read_10_Enconder) refers to ioi2c.o(i.i2cRead) for i2cRead
    bsp_motor_iic.o(i.Read_10_Enconder) refers to bsp_motor_iic.o(.data) for buf
    bsp_motor_iic.o(i.Read_10_Enconder) refers to bsp_motor_iic.o(.bss) for Encoder_Offset
    bsp_motor_iic.o(i.Read_ALL_Enconder) refers to ioi2c.o(i.i2cRead) for i2cRead
    bsp_motor_iic.o(i.Read_ALL_Enconder) refers to bsp_motor_iic.o(.data) for buf
    bsp_motor_iic.o(i.Read_ALL_Enconder) refers to bsp_motor_iic.o(.bss) for Encoder_Now
    bsp_motor_iic.o(i.Set_Pluse_Phase) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_Pluse_Phase) refers to bsp_motor_iic.o(.data) for buf_tempPhase
    bsp_motor_iic.o(i.Set_Pluse_line) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_Pluse_line) refers to bsp_motor_iic.o(.data) for buf_templine
    bsp_motor_iic.o(i.Set_Wheel_dis) refers to bsp_motor_iic.o(i.float_to_bytes) for float_to_bytes
    bsp_motor_iic.o(i.Set_Wheel_dis) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_Wheel_dis) refers to bsp_motor_iic.o(.data) for bytes
    bsp_motor_iic.o(i.Set_motor_deadzone) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.Set_motor_deadzone) refers to bsp_motor_iic.o(.data) for buf_tempzone
    bsp_motor_iic.o(i.Set_motor_type) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.char2float) refers to malloc.o(i.malloc) for malloc
    bsp_motor_iic.o(i.char2float) refers to malloc.o(i.free) for free
    bsp_motor_iic.o(i.control_pwm) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.control_pwm) refers to bsp_motor_iic.o(.data) for pwm
    bsp_motor_iic.o(i.control_speed) refers to ioi2c.o(i.i2cWrite) for i2cWrite
    bsp_motor_iic.o(i.control_speed) refers to bsp_motor_iic.o(.data) for speed
    ioi2c.o(i.IIC_Ack) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_NAck) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Read_Byte) refers to ioi2c.o(i.IIC_Ack) for IIC_Ack
    ioi2c.o(i.IIC_Read_Byte) refers to ioi2c.o(i.IIC_NAck) for IIC_NAck
    ioi2c.o(i.IIC_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Start) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Wait_Ack) refers to delay.o(i.delay_us) for delay_us
    ioi2c.o(i.IIC_Wait_Ack) refers to ioi2c.o(i.IIC_Stop) for IIC_Stop
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Start) for IIC_Start
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Send_Byte) for IIC_Send_Byte
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Stop) for IIC_Stop
    ioi2c.o(i.i2cRead) refers to ioi2c.o(i.IIC_Read_Byte) for IIC_Read_Byte
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Start) for IIC_Start
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Send_Byte) for IIC_Send_Byte
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    ioi2c.o(i.i2cWrite) refers to ioi2c.o(i.IIC_Stop) for IIC_Stop
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fadd.o(.text) for __aeabi_fadd
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to asin.o(i.asin) for asin
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to atan2.o(i.atan2) for atan2
    inv_mpu.o(i.MPU6050_DMP_Init) refers to mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) for MPU6050_IIC_IO_Init
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.run_self_test) for run_self_test
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(.data) for gyro_orientation
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.accel_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(i.accel_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(i.accel_self_test) refers to cfcmple.o(.text) for __aeabi_cfcmpeq
    inv_mpu.o(i.accel_self_test) refers to fadd.o(.text) for __aeabi_fsub
    inv_mpu.o(i.accel_self_test) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.get_accel_prod_shift) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.get_accel_prod_shift) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.get_st_biases) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.get_st_biases) refers to ldiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.gyro_self_test) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.gyro_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(i.gyro_self_test) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(i.gyro_self_test) refers to ffltui.o(.text) for __aeabi_ui2f
    inv_mpu.o(i.gyro_self_test) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(i.gyro_self_test) refers to fadd.o(.text) for __aeabi_fsub
    inv_mpu.o(i.gyro_self_test) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    inv_mpu.o(i.gyro_self_test) refers to cfcmple.o(.text) for __aeabi_cfcmple
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(i.inv_row_2_scale) for inv_row_2_scale
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_int_status) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_temperature) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_temperature) refers to ffltui.o(.text) for __aeabi_ui2f
    inv_mpu.o(i.mpu_get_temperature) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(i.mpu_get_temperature) refers to fadd.o(.text) for __aeabi_fsub
    inv_mpu.o(i.mpu_get_temperature) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu.o(i.mpu_get_temperature) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(i.mpu_get_temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_init) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo_stream) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reg_dump) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reset_fifo) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_reset_fifo) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_accel_bias) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_fsr) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_bypass) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_latched) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_lpf) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sensors) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_write_mem) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_sens) for mpu_get_gyro_sens
    inv_mpu.o(i.run_self_test) refers to fflti.o(.text) for __aeabi_i2f
    inv_mpu.o(i.run_self_test) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu.o(i.run_self_test) refers to ffixi.o(.text) for __aeabi_f2iz
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(i.set_int_enable) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for st
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu_dmp_motion_driver.o(i.decode_gesture) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to memseta.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to memseta.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for dmp_memory
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(i.decode_gesture) for decode_gesture
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to ffltui.o(.text) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fdiv.o(.text) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fmul.o(.text) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to ffixui.o(.text) for __aeabi_f2uiz
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    mpu6050.o(i.MPU6050_Init) refers to mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) for MPU6050_IIC_IO_Init
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.mpu6050_write_reg) for mpu6050_write_reg
    mpu6050.o(i.MPU6050_Init) refers to delay.o(i.delay_ms) for delay_ms
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.MPU_Set_Gyro_Fsr) for MPU_Set_Gyro_Fsr
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.MPU_Set_Accel_Fsr) for MPU_Set_Accel_Fsr
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.MPU_Set_Rate) for MPU_Set_Rate
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.mpu6050_read_reg) for mpu6050_read_reg
    mpu6050.o(i.MPU_Get_Accelerometer) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    mpu6050.o(i.MPU_Get_Temperature) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    mpu6050.o(i.MPU_Get_Temperature) refers to dflti.o(.text) for __aeabi_i2d
    mpu6050.o(i.MPU_Get_Temperature) refers to ddiv.o(.text) for __aeabi_ddiv
    mpu6050.o(i.MPU_Get_Temperature) refers to dadd.o(.text) for __aeabi_dadd
    mpu6050.o(i.MPU_Get_Temperature) refers to d2f.o(.text) for __aeabi_d2f
    mpu6050.o(i.MPU_Get_Temperature) refers to fmul.o(.text) for __aeabi_fmul
    mpu6050.o(i.MPU_Get_Temperature) refers to ffixi.o(.text) for __aeabi_f2iz
    mpu6050.o(i.MPU_Set_Accel_Fsr) refers to mpu6050.o(i.mpu6050_write_reg) for mpu6050_write_reg
    mpu6050.o(i.MPU_Set_Gyro_Fsr) refers to mpu6050.o(i.mpu6050_write_reg) for mpu6050_write_reg
    mpu6050.o(i.MPU_Set_LPF) refers to mpu6050.o(i.mpu6050_write_reg) for mpu6050_write_reg
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.mpu6050_write_reg) for mpu6050_write_reg
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Set_LPF) for MPU_Set_LPF
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Start) for MPU6050_IIC_Start
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) for MPU6050_IIC_Send_Byte
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) for MPU6050_IIC_Read_Byte
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Stop) for MPU6050_IIC_Stop
    mpu6050.o(i.mpu6050_read_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    mpu6050.o(i.mpu6050_write) refers to mpu6050_i2c.o(i.MPU6050_IIC_Start) for MPU6050_IIC_Start
    mpu6050.o(i.mpu6050_write) refers to mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) for MPU6050_IIC_Send_Byte
    mpu6050.o(i.mpu6050_write) refers to mpu6050_i2c.o(i.MPU6050_IIC_Stop) for MPU6050_IIC_Stop
    mpu6050.o(i.mpu6050_write_reg) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) refers to sys.o(i.My_GPIO_Init) for My_GPIO_Init
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN) for MPU6050_IIC_SDA_IO_IN
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) refers to mpu6050_i2c.o(i.MPU6050_IIC_Stop) for MPU6050_IIC_Stop
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN) for MPU6050_IIC_SDA_IO_IN
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack) for MPU6050_IIC_Send_Ack
    mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN) refers to sys.o(i.My_GPIO_Init) for My_GPIO_Init
    mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) refers to sys.o(i.My_GPIO_Init) for My_GPIO_Init
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) for MPU6050_IIC_Read_Ack
    mpu6050_i2c.o(i.MPU6050_IIC_Start) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Start) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Stop) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    sys.o(i.My_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    sys.o(i.My_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.asin) refers to dscalb.o(.text) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.__asin$lsc) for __asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.__asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.__asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.__asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.__asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.__asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.__asin$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    asin_x.o(i.__asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.__asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f10x_hd.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(i.GetRotationState), (12 bytes).
    Removing main.o(i.StartRotationAngle), (156 bytes).
    Removing main.o(i.StopRotation), (28 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing usart.o(i.USART1_Send_ArrayU8), (28 bytes).
    Removing usart.o(i._sys_exit), (4 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(.data), (4 bytes).
    Removing bsp_timer.o(i.TIM1_ResetCounter), (12 bytes).
    Removing bsp_timer.o(i.delay_time), (20 bytes).
    Removing bsp_timer.o(i.my_delay), (22 bytes).
    Removing bsp_motor_iic.o(i.Read_10_Enconder), (140 bytes).
    Removing bsp_motor_iic.o(i.Read_ALL_Enconder), (240 bytes).
    Removing bsp_motor_iic.o(i.char2float), (28 bytes).
    Removing bsp_motor_iic.o(.bss), (32 bytes).
    Removing ioi2c.o(i.IIC_Ack), (76 bytes).
    Removing ioi2c.o(i.IIC_NAck), (76 bytes).
    Removing ioi2c.o(i.IIC_Read_Byte), (112 bytes).
    Removing ioi2c.o(i.i2cRead), (118 bytes).
    Removing inv_mpu.o(i.get_ms), (2 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (116 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (10 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (108 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (68 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (28 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (160 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (516 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (504 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (76 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias), (384 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (66 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (72 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (92 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (42 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (48 bytes).
    Removing mpu6050.o(i.MPU_Get_Temperature), (104 bytes).

503 unused section(s) (total 22416 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\BSP\Delay\delay.c                     0x00000000   Number         0  delay.o ABSOLUTE
    ..\BSP\Timer\bsp_timer.c                 0x00000000   Number         0  bsp_timer.o ABSOLUTE
    ..\BSP\Usart1\usart.c                    0x00000000   Number         0  usart.o ABSOLUTE
    ..\BSP\bsp.c                             0x00000000   Number         0  bsp.o ABSOLUTE
    ..\BSP\motor_model\IOI2C.c               0x00000000   Number         0  ioi2c.o ABSOLUTE
    ..\BSP\motor_model\bsp_motor_iic.c       0x00000000   Number         0  bsp_motor_iic.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\FWLib\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_adc.c             0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_bkp.c             0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\FWLib\src\stm32f10x_can.c             0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\FWLib\src\stm32f10x_cec.c             0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\FWLib\src\stm32f10x_crc.c             0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_dac.c             0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\FWLib\src\stm32f10x_dbgmcu.c          0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\FWLib\src\stm32f10x_dma.c             0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\FWLib\src\stm32f10x_exti.c            0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\FWLib\src\stm32f10x_flash.c           0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\FWLib\src\stm32f10x_fsmc.c            0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_gpio.c            0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\FWLib\src\stm32f10x_i2c.c             0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\FWLib\src\stm32f10x_iwdg.c            0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\FWLib\src\stm32f10x_pwr.c             0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\FWLib\src\stm32f10x_rcc.c             0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_rtc.c             0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\FWLib\src\stm32f10x_sdio.c            0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\FWLib\src\stm32f10x_spi.c             0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\FWLib\src\stm32f10x_tim.c             0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\FWLib\src\stm32f10x_usart.c           0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\FWLib\src\stm32f10x_wwdg.c            0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\MPU6050\Mpu6050\MPU6050_I2C.c         0x00000000   Number         0  mpu6050_i2c.o ABSOLUTE
    ..\MPU6050\Mpu6050\inv_mpu.c             0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\MPU6050\Mpu6050\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\MPU6050\Mpu6050\mpu6050.c             0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\MPU6050\Mpu6050\sys.c                 0x00000000   Number         0  sys.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section        0  ldiv.o(.text)
    .text                                    0x080001ca   Section        0  memseta.o(.text)
    .text                                    0x080001ee   Section        0  memcmp.o(.text)
    .text                                    0x08000208   Section        0  fadd.o(.text)
    .text                                    0x080002b8   Section        0  fmul.o(.text)
    .text                                    0x0800031c   Section        0  fdiv.o(.text)
    .text                                    0x08000398   Section        0  dadd.o(.text)
    .text                                    0x080004e6   Section        0  dmul.o(.text)
    .text                                    0x080005ca   Section        0  ddiv.o(.text)
    .text                                    0x080006a8   Section        0  fflti.o(.text)
    .text                                    0x080006ba   Section        0  ffltui.o(.text)
    .text                                    0x080006c4   Section        0  dflti.o(.text)
    .text                                    0x080006e6   Section        0  ffixi.o(.text)
    .text                                    0x08000718   Section        0  ffixui.o(.text)
    .text                                    0x08000740   Section        0  f2d.o(.text)
    .text                                    0x08000768   Section       48  cdcmple.o(.text)
    .text                                    0x08000798   Section        0  d2f.o(.text)
    .text                                    0x080007d0   Section       20  cfcmple.o(.text)
    .text                                    0x080007e4   Section       20  cfrcmple.o(.text)
    .text                                    0x080007f8   Section        0  uldiv.o(.text)
    .text                                    0x0800085a   Section        0  llshl.o(.text)
    .text                                    0x08000878   Section        0  llsshr.o(.text)
    .text                                    0x0800089c   Section        0  iusefp.o(.text)
    .text                                    0x0800089c   Section        0  fepilogue.o(.text)
    .text                                    0x0800090a   Section        0  depilogue.o(.text)
    .text                                    0x080009c4   Section        0  dscalb.o(.text)
    .text                                    0x080009f4   Section       36  init.o(.text)
    .text                                    0x08000a18   Section        0  llushr.o(.text)
    .text                                    0x08000a38   Section        0  dsqrt.o(.text)
    i.AngleDifference                        0x08000adc   Section        0  main.o(i.AngleDifference)
    i.BusFault_Handler                       0x08000b28   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Car_Move                               0x08000b2c   Section        0  main.o(i.Car_Move)
    i.DIY_NVIC_PriorityGroupConfig           0x08000bdc   Section        0  bsp.o(i.DIY_NVIC_PriorityGroupConfig)
    i.DebugMon_Handler                       0x08000c04   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08000c06   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_WriteBit                          0x08000d1c   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x08000d26   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.IIC_Motor_Init                         0x08000d2c   Section        0  bsp_motor_iic.o(i.IIC_Motor_Init)
    i.IIC_Send_Byte                          0x08000d58   Section        0  ioi2c.o(i.IIC_Send_Byte)
    i.IIC_Start                              0x08000dbc   Section        0  ioi2c.o(i.IIC_Start)
    i.IIC_Stop                               0x08000e24   Section        0  ioi2c.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x08000e6c   Section        0  ioi2c.o(i.IIC_Wait_Ack)
    i.IsRotationComplete                     0x08000ed4   Section        0  main.o(i.IsRotationComplete)
    i.JTAG_Set                               0x08000ef0   Section        0  bsp.o(i.JTAG_Set)
    i.MPU6050_DMP_Get_Data                   0x08000f20   Section        0  inv_mpu.o(i.MPU6050_DMP_Get_Data)
    i.MPU6050_DMP_Init                       0x08001144   Section        0  inv_mpu.o(i.MPU6050_DMP_Init)
    i.MPU6050_IIC_IO_Init                    0x08001194   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_IO_Init)
    i.MPU6050_IIC_Read_Ack                   0x080011c0   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack)
    i.MPU6050_IIC_Read_Byte                  0x08001210   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte)
    i.MPU6050_IIC_SDA_IO_IN                  0x08001270   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN)
    i.MPU6050_IIC_SDA_IO_OUT                 0x08001288   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT)
    i.MPU6050_IIC_Send_Ack                   0x080012a0   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack)
    i.MPU6050_IIC_Send_Byte                  0x080012d8   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte)
    i.MPU6050_IIC_Start                      0x0800133c   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Start)
    i.MPU6050_IIC_Stop                       0x08001374   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Stop)
    i.MPU6050_Init                           0x080013b0   Section        0  mpu6050.o(i.MPU6050_Init)
    i.MPU_Get_Accelerometer                  0x08001422   Section        0  mpu6050.o(i.MPU_Get_Accelerometer)
    i.MPU_Get_Gyroscope                      0x08001472   Section        0  mpu6050.o(i.MPU_Get_Gyroscope)
    i.MPU_Set_Accel_Fsr                      0x080014c2   Section        0  mpu6050.o(i.MPU_Set_Accel_Fsr)
    i.MPU_Set_Gyro_Fsr                       0x080014d4   Section        0  mpu6050.o(i.MPU_Set_Gyro_Fsr)
    i.MPU_Set_LPF                            0x080014e6   Section        0  mpu6050.o(i.MPU_Set_LPF)
    i.MPU_Set_Rate                           0x08001522   Section        0  mpu6050.o(i.MPU_Set_Rate)
    i.MemManage_Handler                      0x0800155a   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.My_GPIO_Init                           0x08001560   Section        0  sys.o(i.My_GPIO_Init)
    i.NMI_Handler                            0x08001610   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001614   Section        0  misc.o(i.NVIC_Init)
    i.OLED_Clear                             0x08001684   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080016b0   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x080016fc   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08001754   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08001784   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080017ac   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x0800185a   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x0800186e   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08001890   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowSignedNum                     0x08001904   Section        0  oled.o(i.OLED_ShowSignedNum)
    i.OLED_ShowString                        0x0800196a   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08001992   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x080019b2   Section        0  oled.o(i.OLED_WriteData)
    i.PID_Calculate                          0x080019d4   Section        0  main.o(i.PID_Calculate)
    i.PID_Init                               0x08001ad4   Section        0  main.o(i.PID_Init)
    i.PendSV_Handler                         0x08001afc   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.ProcessRotationControl                 0x08001b00   Section        0  main.o(i.ProcessRotationControl)
    i.RCC_APB1PeriphClockCmd                 0x08001cc8   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001ce8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001d08   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08001ddc   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001dde   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001ddf   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001de8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001de9   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.Set_Pluse_Phase                        0x08001ec8   Section        0  bsp_motor_iic.o(i.Set_Pluse_Phase)
    i.Set_Pluse_line                         0x08001ee8   Section        0  bsp_motor_iic.o(i.Set_Pluse_line)
    i.Set_Wheel_dis                          0x08001f08   Section        0  bsp_motor_iic.o(i.Set_Wheel_dis)
    i.Set_motor_deadzone                     0x08001f28   Section        0  bsp_motor_iic.o(i.Set_motor_deadzone)
    i.Set_motor_type                         0x08001f48   Section        0  bsp_motor_iic.o(i.Set_motor_type)
    i.StartRotation                          0x08001f58   Section        0  main.o(i.StartRotation)
    i.SysTick_CLKSourceConfig                0x0800204c   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08002074   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08002078   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM1_GetCounter                        0x080020d8   Section        0  bsp_timer.o(i.TIM1_GetCounter)
    i.TIM1_Init                              0x080020e4   Section        0  bsp_timer.o(i.TIM1_Init)
    i.TIM1_UP_IRQHandler                     0x08002150   Section        0  main.o(i.TIM1_UP_IRQHandler)
    i.TIM3_IRQHandler                        0x0800218c   Section        0  bsp_timer.o(i.TIM3_IRQHandler)
    i.TIM3_Init                              0x080021cc   Section        0  bsp_timer.o(i.TIM3_Init)
    i.TIM_ClearFlag                          0x0800222c   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_ClearITPendingBit                  0x08002232   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08002238   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08002250   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08002272   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x08002284   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.TestRotation                           0x08002328   Section        0  main.o(i.TestRotation)
    i.USART1_IRQHandler                      0x080023b8   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART1_Send_U8                         0x080023dc   Section        0  usart.o(i.USART1_Send_U8)
    i.USART_Cmd                              0x080023fc   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08002414   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x0800242e   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08002482   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x080024cc   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x080025a4   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x080025ae   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x080025b6   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x080025ba   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x080025e2   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x0800268c   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08002692   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08002696   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x080026a4   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x080026b4   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080026c2   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080026c4   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x080026d4   Section        0  errno.o(i.__set_errno)
    i.accel_self_test                        0x080026e0   Section        0  inv_mpu.o(i.accel_self_test)
    accel_self_test                          0x080026e1   Thumb Code   164  inv_mpu.o(i.accel_self_test)
    i.asin                                   0x08002788   Section        0  asin.o(i.asin)
    i.atan                                   0x080029f8   Section        0  atan.o(i.atan)
    i.atan2                                  0x08002c18   Section        0  atan2.o(i.atan2)
    i.bsp_init                               0x08002d98   Section        0  bsp.o(i.bsp_init)
    i.control_pwm                            0x08002dbc   Section        0  bsp_motor_iic.o(i.control_pwm)
    i.control_speed                          0x08002df8   Section        0  bsp_motor_iic.o(i.control_speed)
    i.decode_gesture                         0x08002e34   Section        0  inv_mpu_dmp_motion_driver.o(i.decode_gesture)
    decode_gesture                           0x08002e35   Thumb Code    94  inv_mpu_dmp_motion_driver.o(i.decode_gesture)
    i.delay_init                             0x08002e98   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08002ee4   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08002f20   Section        0  delay.o(i.delay_us)
    i.dmp_enable_6x_lp_quat                  0x08002f5c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    i.dmp_enable_feature                     0x08002f98   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    i.dmp_enable_gyro_cal                    0x080031b4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    i.dmp_enable_lp_quat                     0x0800320c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    i.dmp_load_motion_driver_firmware        0x08003248   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    i.dmp_read_fifo                          0x08003260   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    i.dmp_set_accel_bias                     0x08003428   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    i.dmp_set_fifo_rate                      0x08003558   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    i.dmp_set_gyro_bias                      0x080035c8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    i.dmp_set_orientation                    0x080036f8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    i.dmp_set_shake_reject_thresh            0x08003830   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    i.dmp_set_shake_reject_time              0x08003868   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    i.dmp_set_shake_reject_timeout           0x0800388e   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    i.dmp_set_tap_axes                       0x080038b4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    i.dmp_set_tap_count                      0x080038fa   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    i.dmp_set_tap_thresh                     0x08003920   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    i.dmp_set_tap_time                       0x08003ac0   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    i.dmp_set_tap_time_multi                 0x08003ae6   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    i.float_to_bytes                         0x08003b0c   Section        0  bsp_motor_iic.o(i.float_to_bytes)
    i.get_accel_prod_shift                   0x08003b10   Section        0  inv_mpu.o(i.get_accel_prod_shift)
    get_accel_prod_shift                     0x08003b11   Thumb Code   168  inv_mpu.o(i.get_accel_prod_shift)
    i.get_st_biases                          0x08003bc4   Section        0  inv_mpu.o(i.get_st_biases)
    get_st_biases                            0x08003bc5   Thumb Code  1132  inv_mpu.o(i.get_st_biases)
    i.gyro_self_test                         0x08004034   Section        0  inv_mpu.o(i.gyro_self_test)
    gyro_self_test                           0x08004035   Thumb Code   256  inv_mpu.o(i.gyro_self_test)
    i.i2cWrite                               0x08004144   Section        0  ioi2c.o(i.i2cWrite)
    i.inv_orientation_matrix_to_scalar       0x080041a4   Section        0  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    i.inv_row_2_scale                        0x080041cc   Section        0  inv_mpu.o(i.inv_row_2_scale)
    i.main                                   0x0800421c   Section        0  main.o(i.main)
    i.mpu6050_read                           0x080044d8   Section        0  mpu6050.o(i.mpu6050_read)
    i.mpu6050_read_reg                       0x08004534   Section        0  mpu6050.o(i.mpu6050_read_reg)
    i.mpu6050_write                          0x0800454a   Section        0  mpu6050.o(i.mpu6050_write)
    i.mpu6050_write_reg                      0x0800458e   Section        0  mpu6050.o(i.mpu6050_write_reg)
    i.mpu_configure_fifo                     0x080045a0   Section        0  inv_mpu.o(i.mpu_configure_fifo)
    i.mpu_get_accel_fsr                      0x08004610   Section        0  inv_mpu.o(i.mpu_get_accel_fsr)
    i.mpu_get_accel_sens                     0x0800465c   Section        0  inv_mpu.o(i.mpu_get_accel_sens)
    i.mpu_get_fifo_config                    0x080046b0   Section        0  inv_mpu.o(i.mpu_get_fifo_config)
    i.mpu_get_gyro_fsr                       0x080046c0   Section        0  inv_mpu.o(i.mpu_get_gyro_fsr)
    i.mpu_get_gyro_sens                      0x08004704   Section        0  inv_mpu.o(i.mpu_get_gyro_sens)
    i.mpu_get_lpf                            0x08004754   Section        0  inv_mpu.o(i.mpu_get_lpf)
    i.mpu_get_sample_rate                    0x080047a4   Section        0  inv_mpu.o(i.mpu_get_sample_rate)
    i.mpu_init                               0x080047c4   Section        0  inv_mpu.o(i.mpu_init)
    i.mpu_load_firmware                      0x08004954   Section        0  inv_mpu.o(i.mpu_load_firmware)
    i.mpu_lp_accel_mode                      0x08004a0c   Section        0  inv_mpu.o(i.mpu_lp_accel_mode)
    i.mpu_read_fifo_stream                   0x08004aec   Section        0  inv_mpu.o(i.mpu_read_fifo_stream)
    i.mpu_read_mem                           0x08004bac   Section        0  inv_mpu.o(i.mpu_read_mem)
    i.mpu_reset_fifo                         0x08004c2c   Section        0  inv_mpu.o(i.mpu_reset_fifo)
    i.mpu_run_self_test                      0x08004df4   Section        0  inv_mpu.o(i.mpu_run_self_test)
    i.mpu_set_accel_fsr                      0x08004f10   Section        0  inv_mpu.o(i.mpu_set_accel_fsr)
    i.mpu_set_bypass                         0x08004f94   Section        0  inv_mpu.o(i.mpu_set_bypass)
    i.mpu_set_dmp_state                      0x080050e0   Section        0  inv_mpu.o(i.mpu_set_dmp_state)
    i.mpu_set_gyro_fsr                       0x08005170   Section        0  inv_mpu.o(i.mpu_set_gyro_fsr)
    i.mpu_set_int_latched                    0x080051f8   Section        0  inv_mpu.o(i.mpu_set_int_latched)
    i.mpu_set_lpf                            0x08005264   Section        0  inv_mpu.o(i.mpu_set_lpf)
    i.mpu_set_sample_rate                    0x080052e8   Section        0  inv_mpu.o(i.mpu_set_sample_rate)
    i.mpu_set_sensors                        0x08005384   Section        0  inv_mpu.o(i.mpu_set_sensors)
    i.mpu_write_mem                          0x08005454   Section        0  inv_mpu.o(i.mpu_write_mem)
    i.run_self_test                          0x080054d4   Section        0  inv_mpu.o(i.run_self_test)
    i.set_int_enable                         0x08005568   Section        0  inv_mpu.o(i.set_int_enable)
    set_int_enable                           0x08005569   Thumb Code   138  inv_mpu.o(i.set_int_enable)
    i.sqrt                                   0x080055f8   Section        0  sqrt.o(i.sqrt)
    i.uart_init                              0x08005644   Section        0  usart.o(i.uart_init)
    .constdata                               0x080056e4   Section     1520  oled.o(.constdata)
    .constdata                               0x08005cd4   Section       80  inv_mpu.o(.constdata)
    .constdata                               0x08005d24   Section     3062  inv_mpu_dmp_motion_driver.o(.constdata)
    dmp_memory                               0x08005d24   Data        3062  inv_mpu_dmp_motion_driver.o(.constdata)
    .constdata                               0x08006920   Section       80  asin.o(.constdata)
    pS                                       0x08006920   Data          48  asin.o(.constdata)
    qS                                       0x08006950   Data          32  asin.o(.constdata)
    .constdata                               0x08006970   Section      152  atan.o(.constdata)
    atanhi                                   0x08006970   Data          32  atan.o(.constdata)
    atanlo                                   0x08006990   Data          32  atan.o(.constdata)
    aTodd                                    0x080069b0   Data          40  atan.o(.constdata)
    aTeven                                   0x080069d8   Data          48  atan.o(.constdata)
    .constdata                               0x08006a08   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section       78  main.o(.data)
    tim1_counter                             0x20000000   Data           4  main.o(.data)
    last_tim1_counter                        0x20000008   Data           4  main.o(.data)
    led_state                                0x2000000c   Data           1  main.o(.data)
    last_derivative                          0x2000003c   Data           4  main.o(.data)
    state                                    0x20000040   Data           1  main.o(.data)
    test_state                               0x20000041   Data           1  main.o(.data)
    last_time                                0x20000044   Data           4  main.o(.data)
    last_motor_speed                         0x20000048   Data           4  main.o(.data)
    stable_counter                           0x2000004c   Data           2  main.o(.data)
    .data                                    0x20000050   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000064   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000064   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000074   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000078   Section        4  delay.o(.data)
    fac_us                                   0x20000078   Data           1  delay.o(.data)
    fac_ms                                   0x2000007a   Data           2  delay.o(.data)
    .data                                    0x2000007c   Section        8  bsp_timer.o(.data)
    stop_time                                0x2000007c   Data           2  bsp_timer.o(.data)
    tim1_counter                             0x20000080   Data           4  bsp_timer.o(.data)
    .data                                    0x20000084   Section       32  bsp_motor_iic.o(.data)
    buf_tempzone                             0x20000084   Data           2  bsp_motor_iic.o(.data)
    buf_templine                             0x20000086   Data           2  bsp_motor_iic.o(.data)
    buf_tempPhase                            0x20000088   Data           2  bsp_motor_iic.o(.data)
    bytes                                    0x2000008a   Data           4  bsp_motor_iic.o(.data)
    speed                                    0x2000008e   Data           8  bsp_motor_iic.o(.data)
    pwm                                      0x20000096   Data           8  bsp_motor_iic.o(.data)
    buf                                      0x2000009e   Data           2  bsp_motor_iic.o(.data)
    buf                                      0x200000a0   Data           2  bsp_motor_iic.o(.data)
    buf2                                     0x200000a2   Data           2  bsp_motor_iic.o(.data)
    .data                                    0x200000a4   Section       53  inv_mpu.o(.data)
    st                                       0x200000a4   Data          44  inv_mpu.o(.data)
    gyro_orientation                         0x200000d0   Data           9  inv_mpu.o(.data)
    .data                                    0x200000dc   Section        4  errno.o(.data)
    _errno                                   0x200000dc   Data           4  errno.o(.data)
    .bss                                     0x200000e0   Section       52  main.o(.bss)
    .bss                                     0x20000114   Section       16  inv_mpu_dmp_motion_driver.o(.bss)
    dmp                                      0x20000114   Data          16  inv_mpu_dmp_motion_driver.o(.bss)
    STACK                                    0x20000128   Section     1024  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_ldivmod                          0x08000169   Thumb Code    98  ldiv.o(.text)
    __aeabi_memset                           0x080001cb   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080001cb   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080001cb   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080001d9   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080001d9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080001d9   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080001dd   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x080001ef   Thumb Code    26  memcmp.o(.text)
    __aeabi_fadd                             0x08000209   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x080002ad   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x080002b3   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x080002b9   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x0800031d   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x08000399   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080004db   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080004e1   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080004e7   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080005cb   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2f                              0x080006a9   Thumb Code    18  fflti.o(.text)
    __aeabi_ui2f                             0x080006bb   Thumb Code    10  ffltui.o(.text)
    __aeabi_i2d                              0x080006c5   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x080006e7   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x08000719   Thumb Code    40  ffixui.o(.text)
    __aeabi_f2d                              0x08000741   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x08000769   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000769   Thumb Code    48  cdcmple.o(.text)
    __aeabi_d2f                              0x08000799   Thumb Code    56  d2f.o(.text)
    __aeabi_cfcmpeq                          0x080007d1   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x080007d1   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x080007e5   Thumb Code    20  cfrcmple.o(.text)
    __aeabi_uldivmod                         0x080007f9   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x0800085b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800085b   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000879   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000879   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0800089d   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800089d   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080008af   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800090b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000929   Thumb Code   156  depilogue.o(.text)
    __ARM_scalbn                             0x080009c5   Thumb Code    46  dscalb.o(.text)
    scalbn                                   0x080009c5   Thumb Code     0  dscalb.o(.text)
    __scatterload                            0x080009f5   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080009f5   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x08000a19   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000a19   Thumb Code     0  llushr.o(.text)
    _dsqrt                                   0x08000a39   Thumb Code   162  dsqrt.o(.text)
    AngleDifference                          0x08000add   Thumb Code    64  main.o(i.AngleDifference)
    BusFault_Handler                         0x08000b29   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Car_Move                                 0x08000b2d   Thumb Code   154  main.o(i.Car_Move)
    DIY_NVIC_PriorityGroupConfig             0x08000bdd   Thumb Code    32  bsp.o(i.DIY_NVIC_PriorityGroupConfig)
    DebugMon_Handler                         0x08000c05   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08000c07   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_WriteBit                            0x08000d1d   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x08000d27   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    IIC_Motor_Init                           0x08000d2d   Thumb Code    40  bsp_motor_iic.o(i.IIC_Motor_Init)
    IIC_Send_Byte                            0x08000d59   Thumb Code    88  ioi2c.o(i.IIC_Send_Byte)
    IIC_Start                                0x08000dbd   Thumb Code    92  ioi2c.o(i.IIC_Start)
    IIC_Stop                                 0x08000e25   Thumb Code    60  ioi2c.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x08000e6d   Thumb Code    92  ioi2c.o(i.IIC_Wait_Ack)
    IsRotationComplete                       0x08000ed5   Thumb Code    22  main.o(i.IsRotationComplete)
    JTAG_Set                                 0x08000ef1   Thumb Code    38  bsp.o(i.JTAG_Set)
    MPU6050_DMP_Get_Data                     0x08000f21   Thumb Code   544  inv_mpu.o(i.MPU6050_DMP_Get_Data)
    MPU6050_DMP_Init                         0x08001145   Thumb Code    74  inv_mpu.o(i.MPU6050_DMP_Init)
    MPU6050_IIC_IO_Init                      0x08001195   Thumb Code    32  mpu6050_i2c.o(i.MPU6050_IIC_IO_Init)
    MPU6050_IIC_Read_Ack                     0x080011c1   Thumb Code    72  mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack)
    MPU6050_IIC_Read_Byte                    0x08001211   Thumb Code    86  mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte)
    MPU6050_IIC_SDA_IO_IN                    0x08001271   Thumb Code    20  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN)
    MPU6050_IIC_SDA_IO_OUT                   0x08001289   Thumb Code    20  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT)
    MPU6050_IIC_Send_Ack                     0x080012a1   Thumb Code    48  mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack)
    MPU6050_IIC_Send_Byte                    0x080012d9   Thumb Code    90  mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte)
    MPU6050_IIC_Start                        0x0800133d   Thumb Code    46  mpu6050_i2c.o(i.MPU6050_IIC_Start)
    MPU6050_IIC_Stop                         0x08001375   Thumb Code    52  mpu6050_i2c.o(i.MPU6050_IIC_Stop)
    MPU6050_Init                             0x080013b1   Thumb Code   114  mpu6050.o(i.MPU6050_Init)
    MPU_Get_Accelerometer                    0x08001423   Thumb Code    80  mpu6050.o(i.MPU_Get_Accelerometer)
    MPU_Get_Gyroscope                        0x08001473   Thumb Code    80  mpu6050.o(i.MPU_Get_Gyroscope)
    MPU_Set_Accel_Fsr                        0x080014c3   Thumb Code    18  mpu6050.o(i.MPU_Set_Accel_Fsr)
    MPU_Set_Gyro_Fsr                         0x080014d5   Thumb Code    18  mpu6050.o(i.MPU_Set_Gyro_Fsr)
    MPU_Set_LPF                              0x080014e7   Thumb Code    60  mpu6050.o(i.MPU_Set_LPF)
    MPU_Set_Rate                             0x08001523   Thumb Code    56  mpu6050.o(i.MPU_Set_Rate)
    MemManage_Handler                        0x0800155b   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    My_GPIO_Init                             0x08001561   Thumb Code   148  sys.o(i.My_GPIO_Init)
    NMI_Handler                              0x08001611   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001615   Thumb Code   100  misc.o(i.NVIC_Init)
    OLED_Clear                               0x08001685   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080016b1   Thumb Code    72  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x080016fd   Thumb Code    82  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08001755   Thumb Code    44  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08001785   Thumb Code    34  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080017ad   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_Pow                                 0x0800185b   Thumb Code    20  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x0800186f   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08001891   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowSignedNum                       0x08001905   Thumb Code   102  oled.o(i.OLED_ShowSignedNum)
    OLED_ShowString                          0x0800196b   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08001993   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x080019b3   Thumb Code    32  oled.o(i.OLED_WriteData)
    PID_Calculate                            0x080019d5   Thumb Code   236  main.o(i.PID_Calculate)
    PID_Init                                 0x08001ad5   Thumb Code    40  main.o(i.PID_Init)
    PendSV_Handler                           0x08001afd   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    ProcessRotationControl                   0x08001b01   Thumb Code   402  main.o(i.ProcessRotationControl)
    RCC_APB1PeriphClockCmd                   0x08001cc9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001ce9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001d09   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08001ddd   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Set_Pluse_Phase                          0x08001ec9   Thumb Code    26  bsp_motor_iic.o(i.Set_Pluse_Phase)
    Set_Pluse_line                           0x08001ee9   Thumb Code    26  bsp_motor_iic.o(i.Set_Pluse_line)
    Set_Wheel_dis                            0x08001f09   Thumb Code    26  bsp_motor_iic.o(i.Set_Wheel_dis)
    Set_motor_deadzone                       0x08001f29   Thumb Code    26  bsp_motor_iic.o(i.Set_motor_deadzone)
    Set_motor_type                           0x08001f49   Thumb Code    16  bsp_motor_iic.o(i.Set_motor_type)
    StartRotation                            0x08001f59   Thumb Code   190  main.o(i.StartRotation)
    SysTick_CLKSourceConfig                  0x0800204d   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08002075   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08002079   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM1_GetCounter                          0x080020d9   Thumb Code     6  bsp_timer.o(i.TIM1_GetCounter)
    TIM1_Init                                0x080020e5   Thumb Code   102  bsp_timer.o(i.TIM1_Init)
    TIM1_UP_IRQHandler                       0x08002151   Thumb Code    50  main.o(i.TIM1_UP_IRQHandler)
    TIM3_IRQHandler                          0x0800218d   Thumb Code    50  bsp_timer.o(i.TIM3_IRQHandler)
    TIM3_Init                                0x080021cd   Thumb Code    90  bsp_timer.o(i.TIM3_Init)
    TIM_ClearFlag                            0x0800222d   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_ClearITPendingBit                    0x08002233   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08002239   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08002251   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08002273   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x08002285   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    TestRotation                             0x08002329   Thumb Code   134  main.o(i.TestRotation)
    USART1_IRQHandler                        0x080023b9   Thumb Code    32  usart.o(i.USART1_IRQHandler)
    USART1_Send_U8                           0x080023dd   Thumb Code    28  usart.o(i.USART1_Send_U8)
    USART_Cmd                                0x080023fd   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08002415   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x0800242f   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08002483   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x080024cd   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x080025a5   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x080025af   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x080025b7   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080025bb   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x080025e3   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x0800268d   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08002693   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08002697   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x080026a5   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x080026b5   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080026c3   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080026c5   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x080026d5   Thumb Code     6  errno.o(i.__set_errno)
    asin                                     0x08002789   Thumb Code   572  asin.o(i.asin)
    atan                                     0x080029f9   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x08002c19   Thumb Code   346  atan2.o(i.atan2)
    bsp_init                                 0x08002d99   Thumb Code    34  bsp.o(i.bsp_init)
    control_pwm                              0x08002dbd   Thumb Code    54  bsp_motor_iic.o(i.control_pwm)
    control_speed                            0x08002df9   Thumb Code    54  bsp_motor_iic.o(i.control_speed)
    delay_init                               0x08002e99   Thumb Code    60  delay.o(i.delay_init)
    delay_ms                                 0x08002ee5   Thumb Code    56  delay.o(i.delay_ms)
    delay_us                                 0x08002f21   Thumb Code    56  delay.o(i.delay_us)
    dmp_enable_6x_lp_quat                    0x08002f5d   Thumb Code    60  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    dmp_enable_feature                       0x08002f99   Thumb Code   530  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    dmp_enable_gyro_cal                      0x080031b5   Thumb Code    62  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    dmp_enable_lp_quat                       0x0800320d   Thumb Code    60  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    dmp_load_motion_driver_firmware          0x08003249   Thumb Code    20  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x08003261   Thumb Code   450  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    dmp_set_accel_bias                       0x08003429   Thumb Code   300  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    dmp_set_fifo_rate                        0x08003559   Thumb Code    96  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    dmp_set_gyro_bias                        0x080035c9   Thumb Code   294  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    dmp_set_orientation                      0x080036f9   Thumb Code   290  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    dmp_set_shake_reject_thresh              0x08003831   Thumb Code    56  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    dmp_set_shake_reject_time                0x08003869   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    dmp_set_shake_reject_timeout             0x0800388f   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    dmp_set_tap_axes                         0x080038b5   Thumb Code    70  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    dmp_set_tap_count                        0x080038fb   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    dmp_set_tap_thresh                       0x08003921   Thumb Code   396  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    dmp_set_tap_time                         0x08003ac1   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    dmp_set_tap_time_multi                   0x08003ae7   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    float_to_bytes                           0x08003b0d   Thumb Code     4  bsp_motor_iic.o(i.float_to_bytes)
    i2cWrite                                 0x08004145   Thumb Code    96  ioi2c.o(i.i2cWrite)
    inv_orientation_matrix_to_scalar         0x080041a5   Thumb Code    40  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    inv_row_2_scale                          0x080041cd   Thumb Code    78  inv_mpu.o(i.inv_row_2_scale)
    main                                     0x0800421d   Thumb Code   542  main.o(i.main)
    mpu6050_read                             0x080044d9   Thumb Code    92  mpu6050.o(i.mpu6050_read)
    mpu6050_read_reg                         0x08004535   Thumb Code    22  mpu6050.o(i.mpu6050_read_reg)
    mpu6050_write                            0x0800454b   Thumb Code    68  mpu6050.o(i.mpu6050_write)
    mpu6050_write_reg                        0x0800458f   Thumb Code    18  mpu6050.o(i.mpu6050_write_reg)
    mpu_configure_fifo                       0x080045a1   Thumb Code   106  inv_mpu.o(i.mpu_configure_fifo)
    mpu_get_accel_fsr                        0x08004611   Thumb Code    72  inv_mpu.o(i.mpu_get_accel_fsr)
    mpu_get_accel_sens                       0x0800465d   Thumb Code    78  inv_mpu.o(i.mpu_get_accel_sens)
    mpu_get_fifo_config                      0x080046b1   Thumb Code    12  inv_mpu.o(i.mpu_get_fifo_config)
    mpu_get_gyro_fsr                         0x080046c1   Thumb Code    64  inv_mpu.o(i.mpu_get_gyro_fsr)
    mpu_get_gyro_sens                        0x08004705   Thumb Code    58  inv_mpu.o(i.mpu_get_gyro_sens)
    mpu_get_lpf                              0x08004755   Thumb Code    74  inv_mpu.o(i.mpu_get_lpf)
    mpu_get_sample_rate                      0x080047a5   Thumb Code    26  inv_mpu.o(i.mpu_get_sample_rate)
    mpu_init                                 0x080047c5   Thumb Code   396  inv_mpu.o(i.mpu_init)
    mpu_load_firmware                        0x08004955   Thumb Code   180  inv_mpu.o(i.mpu_load_firmware)
    mpu_lp_accel_mode                        0x08004a0d   Thumb Code   218  inv_mpu.o(i.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x08004aed   Thumb Code   186  inv_mpu.o(i.mpu_read_fifo_stream)
    mpu_read_mem                             0x08004bad   Thumb Code   122  inv_mpu.o(i.mpu_read_mem)
    mpu_reset_fifo                           0x08004c2d   Thumb Code   450  inv_mpu.o(i.mpu_reset_fifo)
    mpu_run_self_test                        0x08004df5   Thumb Code   278  inv_mpu.o(i.mpu_run_self_test)
    mpu_set_accel_fsr                        0x08004f11   Thumb Code   126  inv_mpu.o(i.mpu_set_accel_fsr)
    mpu_set_bypass                           0x08004f95   Thumb Code   328  inv_mpu.o(i.mpu_set_bypass)
    mpu_set_dmp_state                        0x080050e1   Thumb Code   138  inv_mpu.o(i.mpu_set_dmp_state)
    mpu_set_gyro_fsr                         0x08005171   Thumb Code   132  inv_mpu.o(i.mpu_set_gyro_fsr)
    mpu_set_int_latched                      0x080051f9   Thumb Code   102  inv_mpu.o(i.mpu_set_int_latched)
    mpu_set_lpf                              0x08005265   Thumb Code   126  inv_mpu.o(i.mpu_set_lpf)
    mpu_set_sample_rate                      0x080052e9   Thumb Code   152  inv_mpu.o(i.mpu_set_sample_rate)
    mpu_set_sensors                          0x08005385   Thumb Code   202  inv_mpu.o(i.mpu_set_sensors)
    mpu_write_mem                            0x08005455   Thumb Code   122  inv_mpu.o(i.mpu_write_mem)
    run_self_test                            0x080054d5   Thumb Code   148  inv_mpu.o(i.run_self_test)
    sqrt                                     0x080055f9   Thumb Code    76  sqrt.o(i.sqrt)
    uart_init                                0x08005645   Thumb Code   152  usart.o(i.uart_init)
    OLED_F8x16                               0x080056e4   Data        1520  oled.o(.constdata)
    hw                                       0x08005cd4   Data          12  inv_mpu.o(.constdata)
    reg                                      0x08005ce0   Data          27  inv_mpu.o(.constdata)
    test                                     0x08005cfc   Data          40  inv_mpu.o(.constdata)
    __mathlib_zero                           0x08006a08   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08006a10   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006a30   Number         0  anon$$obj.o(Region$$Table)
    times                                    0x20000004   Data           1  main.o(.data)
    Pitch                                    0x20000010   Data           4  main.o(.data)
    Roll                                     0x20000014   Data           4  main.o(.data)
    Yaw                                      0x20000018   Data           4  main.o(.data)
    ax                                       0x2000001c   Data           2  main.o(.data)
    ay                                       0x2000001e   Data           2  main.o(.data)
    az                                       0x20000020   Data           2  main.o(.data)
    gx                                       0x20000022   Data           2  main.o(.data)
    gy                                       0x20000024   Data           2  main.o(.data)
    gz                                       0x20000026   Data           2  main.o(.data)
    rotation_state                           0x20000028   Data           1  main.o(.data)
    target_yaw                               0x2000002c   Data           4  main.o(.data)
    initial_yaw                              0x20000030   Data           4  main.o(.data)
    rotation_timeout                         0x20000034   Data           4  main.o(.data)
    rotation_direction                       0x20000038   Data           1  main.o(.data)
    SystemCoreClock                          0x20000050   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000054   Data          16  system_stm32f10x.o(.data)
    yaw_pid                                  0x200000e0   Data          52  main.o(.bss)
    __initial_sp                             0x20000528   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006b10, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006a30, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          538    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO         4517  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         4623    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         4626    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4628    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         4630    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         4631    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         4633    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         4635    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         4624    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          539    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000062   Code   RO         4520    .text               mc_w.l(ldiv.o)
    0x080001ca   0x080001ca   0x00000024   Code   RO         4522    .text               mc_w.l(memseta.o)
    0x080001ee   0x080001ee   0x0000001a   Code   RO         4524    .text               mc_w.l(memcmp.o)
    0x08000208   0x08000208   0x000000b0   Code   RO         4554    .text               mf_w.l(fadd.o)
    0x080002b8   0x080002b8   0x00000064   Code   RO         4556    .text               mf_w.l(fmul.o)
    0x0800031c   0x0800031c   0x0000007c   Code   RO         4558    .text               mf_w.l(fdiv.o)
    0x08000398   0x08000398   0x0000014e   Code   RO         4560    .text               mf_w.l(dadd.o)
    0x080004e6   0x080004e6   0x000000e4   Code   RO         4562    .text               mf_w.l(dmul.o)
    0x080005ca   0x080005ca   0x000000de   Code   RO         4564    .text               mf_w.l(ddiv.o)
    0x080006a8   0x080006a8   0x00000012   Code   RO         4566    .text               mf_w.l(fflti.o)
    0x080006ba   0x080006ba   0x0000000a   Code   RO         4568    .text               mf_w.l(ffltui.o)
    0x080006c4   0x080006c4   0x00000022   Code   RO         4570    .text               mf_w.l(dflti.o)
    0x080006e6   0x080006e6   0x00000032   Code   RO         4572    .text               mf_w.l(ffixi.o)
    0x08000718   0x08000718   0x00000028   Code   RO         4574    .text               mf_w.l(ffixui.o)
    0x08000740   0x08000740   0x00000026   Code   RO         4576    .text               mf_w.l(f2d.o)
    0x08000766   0x08000766   0x00000002   PAD
    0x08000768   0x08000768   0x00000030   Code   RO         4578    .text               mf_w.l(cdcmple.o)
    0x08000798   0x08000798   0x00000038   Code   RO         4580    .text               mf_w.l(d2f.o)
    0x080007d0   0x080007d0   0x00000014   Code   RO         4582    .text               mf_w.l(cfcmple.o)
    0x080007e4   0x080007e4   0x00000014   Code   RO         4584    .text               mf_w.l(cfrcmple.o)
    0x080007f8   0x080007f8   0x00000062   Code   RO         4637    .text               mc_w.l(uldiv.o)
    0x0800085a   0x0800085a   0x0000001e   Code   RO         4639    .text               mc_w.l(llshl.o)
    0x08000878   0x08000878   0x00000024   Code   RO         4641    .text               mc_w.l(llsshr.o)
    0x0800089c   0x0800089c   0x00000000   Code   RO         4656    .text               mc_w.l(iusefp.o)
    0x0800089c   0x0800089c   0x0000006e   Code   RO         4657    .text               mf_w.l(fepilogue.o)
    0x0800090a   0x0800090a   0x000000ba   Code   RO         4659    .text               mf_w.l(depilogue.o)
    0x080009c4   0x080009c4   0x0000002e   Code   RO         4661    .text               mf_w.l(dscalb.o)
    0x080009f2   0x080009f2   0x00000002   PAD
    0x080009f4   0x080009f4   0x00000024   Code   RO         4663    .text               mc_w.l(init.o)
    0x08000a18   0x08000a18   0x00000020   Code   RO         4665    .text               mc_w.l(llushr.o)
    0x08000a38   0x08000a38   0x000000a2   Code   RO         4667    .text               mf_w.l(dsqrt.o)
    0x08000ada   0x08000ada   0x00000002   PAD
    0x08000adc   0x08000adc   0x0000004c   Code   RO            1    i.AngleDifference   main.o
    0x08000b28   0x08000b28   0x00000004   Code   RO          237    i.BusFault_Handler  stm32f10x_it.o
    0x08000b2c   0x08000b2c   0x000000b0   Code   RO            2    i.Car_Move          main.o
    0x08000bdc   0x08000bdc   0x00000028   Code   RO         3539    i.DIY_NVIC_PriorityGroupConfig  bsp.o
    0x08000c04   0x08000c04   0x00000002   Code   RO          238    i.DebugMon_Handler  stm32f10x_it.o
    0x08000c06   0x08000c06   0x00000116   Code   RO         1694    i.GPIO_Init         stm32f10x_gpio.o
    0x08000d1c   0x08000d1c   0x0000000a   Code   RO         1705    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000d26   0x08000d26   0x00000004   Code   RO          239    i.HardFault_Handler  stm32f10x_it.o
    0x08000d2a   0x08000d2a   0x00000002   PAD
    0x08000d2c   0x08000d2c   0x0000002c   Code   RO         3676    i.IIC_Motor_Init    bsp_motor_iic.o
    0x08000d58   0x08000d58   0x00000064   Code   RO         3767    i.IIC_Send_Byte     ioi2c.o
    0x08000dbc   0x08000dbc   0x00000068   Code   RO         3768    i.IIC_Start         ioi2c.o
    0x08000e24   0x08000e24   0x00000048   Code   RO         3769    i.IIC_Stop          ioi2c.o
    0x08000e6c   0x08000e6c   0x00000068   Code   RO         3770    i.IIC_Wait_Ack      ioi2c.o
    0x08000ed4   0x08000ed4   0x0000001c   Code   RO            4    i.IsRotationComplete  main.o
    0x08000ef0   0x08000ef0   0x00000030   Code   RO         3540    i.JTAG_Set          bsp.o
    0x08000f20   0x08000f20   0x00000224   Code   RO         3831    i.MPU6050_DMP_Get_Data  inv_mpu.o
    0x08001144   0x08001144   0x00000050   Code   RO         3832    i.MPU6050_DMP_Init  inv_mpu.o
    0x08001194   0x08001194   0x0000002c   Code   RO         4427    i.MPU6050_IIC_IO_Init  mpu6050_i2c.o
    0x080011c0   0x080011c0   0x00000050   Code   RO         4428    i.MPU6050_IIC_Read_Ack  mpu6050_i2c.o
    0x08001210   0x08001210   0x00000060   Code   RO         4429    i.MPU6050_IIC_Read_Byte  mpu6050_i2c.o
    0x08001270   0x08001270   0x00000018   Code   RO         4430    i.MPU6050_IIC_SDA_IO_IN  mpu6050_i2c.o
    0x08001288   0x08001288   0x00000018   Code   RO         4431    i.MPU6050_IIC_SDA_IO_OUT  mpu6050_i2c.o
    0x080012a0   0x080012a0   0x00000038   Code   RO         4432    i.MPU6050_IIC_Send_Ack  mpu6050_i2c.o
    0x080012d8   0x080012d8   0x00000064   Code   RO         4433    i.MPU6050_IIC_Send_Byte  mpu6050_i2c.o
    0x0800133c   0x0800133c   0x00000038   Code   RO         4434    i.MPU6050_IIC_Start  mpu6050_i2c.o
    0x08001374   0x08001374   0x0000003c   Code   RO         4435    i.MPU6050_IIC_Stop  mpu6050_i2c.o
    0x080013b0   0x080013b0   0x00000072   Code   RO         4346    i.MPU6050_Init      mpu6050.o
    0x08001422   0x08001422   0x00000050   Code   RO         4347    i.MPU_Get_Accelerometer  mpu6050.o
    0x08001472   0x08001472   0x00000050   Code   RO         4348    i.MPU_Get_Gyroscope  mpu6050.o
    0x080014c2   0x080014c2   0x00000012   Code   RO         4350    i.MPU_Set_Accel_Fsr  mpu6050.o
    0x080014d4   0x080014d4   0x00000012   Code   RO         4351    i.MPU_Set_Gyro_Fsr  mpu6050.o
    0x080014e6   0x080014e6   0x0000003c   Code   RO         4352    i.MPU_Set_LPF       mpu6050.o
    0x08001522   0x08001522   0x00000038   Code   RO         4353    i.MPU_Set_Rate      mpu6050.o
    0x0800155a   0x0800155a   0x00000004   Code   RO          240    i.MemManage_Handler  stm32f10x_it.o
    0x0800155e   0x0800155e   0x00000002   PAD
    0x08001560   0x08001560   0x000000b0   Code   RO         4487    i.My_GPIO_Init      sys.o
    0x08001610   0x08001610   0x00000002   Code   RO          241    i.NMI_Handler       stm32f10x_it.o
    0x08001612   0x08001612   0x00000002   PAD
    0x08001614   0x08001614   0x00000070   Code   RO          543    i.NVIC_Init         misc.o
    0x08001684   0x08001684   0x0000002a   Code   RO          383    i.OLED_Clear        oled.o
    0x080016ae   0x080016ae   0x00000002   PAD
    0x080016b0   0x080016b0   0x0000004c   Code   RO          384    i.OLED_I2C_Init     oled.o
    0x080016fc   0x080016fc   0x00000058   Code   RO          385    i.OLED_I2C_SendByte  oled.o
    0x08001754   0x08001754   0x00000030   Code   RO          386    i.OLED_I2C_Start    oled.o
    0x08001784   0x08001784   0x00000028   Code   RO          387    i.OLED_I2C_Stop     oled.o
    0x080017ac   0x080017ac   0x000000ae   Code   RO          388    i.OLED_Init         oled.o
    0x0800185a   0x0800185a   0x00000014   Code   RO          389    i.OLED_Pow          oled.o
    0x0800186e   0x0800186e   0x00000022   Code   RO          390    i.OLED_SetCursor    oled.o
    0x08001890   0x08001890   0x00000074   Code   RO          392    i.OLED_ShowChar     oled.o
    0x08001904   0x08001904   0x00000066   Code   RO          395    i.OLED_ShowSignedNum  oled.o
    0x0800196a   0x0800196a   0x00000028   Code   RO          396    i.OLED_ShowString   oled.o
    0x08001992   0x08001992   0x00000020   Code   RO          397    i.OLED_WriteCommand  oled.o
    0x080019b2   0x080019b2   0x00000020   Code   RO          398    i.OLED_WriteData    oled.o
    0x080019d2   0x080019d2   0x00000002   PAD
    0x080019d4   0x080019d4   0x00000100   Code   RO            5    i.PID_Calculate     main.o
    0x08001ad4   0x08001ad4   0x00000028   Code   RO            6    i.PID_Init          main.o
    0x08001afc   0x08001afc   0x00000002   Code   RO          242    i.PendSV_Handler    stm32f10x_it.o
    0x08001afe   0x08001afe   0x00000002   PAD
    0x08001b00   0x08001b00   0x000001c8   Code   RO            7    i.ProcessRotationControl  main.o
    0x08001cc8   0x08001cc8   0x00000020   Code   RO         2110    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001ce8   0x08001ce8   0x00000020   Code   RO         2112    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001d08   0x08001d08   0x000000d4   Code   RO         2120    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08001ddc   0x08001ddc   0x00000002   Code   RO          243    i.SVC_Handler       stm32f10x_it.o
    0x08001dde   0x08001dde   0x00000008   Code   RO          491    i.SetSysClock       system_stm32f10x.o
    0x08001de6   0x08001de6   0x00000002   PAD
    0x08001de8   0x08001de8   0x000000e0   Code   RO          492    i.SetSysClockTo72   system_stm32f10x.o
    0x08001ec8   0x08001ec8   0x00000020   Code   RO         3679    i.Set_Pluse_Phase   bsp_motor_iic.o
    0x08001ee8   0x08001ee8   0x00000020   Code   RO         3680    i.Set_Pluse_line    bsp_motor_iic.o
    0x08001f08   0x08001f08   0x00000020   Code   RO         3681    i.Set_Wheel_dis     bsp_motor_iic.o
    0x08001f28   0x08001f28   0x00000020   Code   RO         3682    i.Set_motor_deadzone  bsp_motor_iic.o
    0x08001f48   0x08001f48   0x00000010   Code   RO         3683    i.Set_motor_type    bsp_motor_iic.o
    0x08001f58   0x08001f58   0x000000f4   Code   RO            8    i.StartRotation     main.o
    0x0800204c   0x0800204c   0x00000028   Code   RO          547    i.SysTick_CLKSourceConfig  misc.o
    0x08002074   0x08002074   0x00000002   Code   RO          244    i.SysTick_Handler   stm32f10x_it.o
    0x08002076   0x08002076   0x00000002   PAD
    0x08002078   0x08002078   0x00000060   Code   RO          494    i.SystemInit        system_stm32f10x.o
    0x080020d8   0x080020d8   0x0000000c   Code   RO         3626    i.TIM1_GetCounter   bsp_timer.o
    0x080020e4   0x080020e4   0x0000006c   Code   RO         3627    i.TIM1_Init         bsp_timer.o
    0x08002150   0x08002150   0x0000003c   Code   RO           11    i.TIM1_UP_IRQHandler  main.o
    0x0800218c   0x0800218c   0x00000040   Code   RO         3629    i.TIM3_IRQHandler   bsp_timer.o
    0x080021cc   0x080021cc   0x00000060   Code   RO         3630    i.TIM3_Init         bsp_timer.o
    0x0800222c   0x0800222c   0x00000006   Code   RO         2738    i.TIM_ClearFlag     stm32f10x_tim.o
    0x08002232   0x08002232   0x00000006   Code   RO         2739    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08002238   0x08002238   0x00000018   Code   RO         2744    i.TIM_Cmd           stm32f10x_tim.o
    0x08002250   0x08002250   0x00000022   Code   RO         2765    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08002272   0x08002272   0x00000012   Code   RO         2769    i.TIM_ITConfig      stm32f10x_tim.o
    0x08002284   0x08002284   0x000000a4   Code   RO         2815    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08002328   0x08002328   0x00000090   Code   RO           12    i.TestRotation      main.o
    0x080023b8   0x080023b8   0x00000024   Code   RO         3573    i.USART1_IRQHandler  usart.o
    0x080023dc   0x080023dc   0x00000020   Code   RO         3575    i.USART1_Send_U8    usart.o
    0x080023fc   0x080023fc   0x00000018   Code   RO         3280    i.USART_Cmd         stm32f10x_usart.o
    0x08002414   0x08002414   0x0000001a   Code   RO         3283    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x0800242e   0x0800242e   0x00000054   Code   RO         3284    i.USART_GetITStatus  stm32f10x_usart.o
    0x08002482   0x08002482   0x0000004a   Code   RO         3286    i.USART_ITConfig    stm32f10x_usart.o
    0x080024cc   0x080024cc   0x000000d8   Code   RO         3287    i.USART_Init        stm32f10x_usart.o
    0x080025a4   0x080025a4   0x0000000a   Code   RO         3294    i.USART_ReceiveData  stm32f10x_usart.o
    0x080025ae   0x080025ae   0x00000008   Code   RO         3297    i.USART_SendData    stm32f10x_usart.o
    0x080025b6   0x080025b6   0x00000004   Code   RO          245    i.UsageFault_Handler  stm32f10x_it.o
    0x080025ba   0x080025ba   0x00000028   Code   RO         4610    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x080025e2   0x080025e2   0x000000aa   Code   RO         4612    i.__kernel_poly     m_ws.l(poly.o)
    0x0800268c   0x0800268c   0x00000006   Code   RO         4597    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x08002692   0x08002692   0x00000004   Code   RO         4598    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x08002696   0x08002696   0x0000000c   Code   RO         4599    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x080026a2   0x080026a2   0x00000002   PAD
    0x080026a4   0x080026a4   0x00000010   Code   RO         4602    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x080026b4   0x080026b4   0x0000000e   Code   RO         4671    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080026c2   0x080026c2   0x00000002   Code   RO         4672    i.__scatterload_null  mc_w.l(handlers.o)
    0x080026c4   0x080026c4   0x0000000e   Code   RO         4673    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080026d2   0x080026d2   0x00000002   PAD
    0x080026d4   0x080026d4   0x0000000c   Code   RO         4651    i.__set_errno       mc_w.l(errno.o)
    0x080026e0   0x080026e0   0x000000a8   Code   RO         3833    i.accel_self_test   inv_mpu.o
    0x08002788   0x08002788   0x00000270   Code   RO         4500    i.asin              m_ws.l(asin.o)
    0x080029f8   0x080029f8   0x00000220   Code   RO         4587    i.atan              m_ws.l(atan.o)
    0x08002c18   0x08002c18   0x00000180   Code   RO         4510    i.atan2             m_ws.l(atan2.o)
    0x08002d98   0x08002d98   0x00000022   Code   RO         3541    i.bsp_init          bsp.o
    0x08002dba   0x08002dba   0x00000002   PAD
    0x08002dbc   0x08002dbc   0x0000003c   Code   RO         3685    i.control_pwm       bsp_motor_iic.o
    0x08002df8   0x08002df8   0x0000003c   Code   RO         3686    i.control_speed     bsp_motor_iic.o
    0x08002e34   0x08002e34   0x00000064   Code   RO         4170    i.decode_gesture    inv_mpu_dmp_motion_driver.o
    0x08002e98   0x08002e98   0x0000004c   Code   RO         3510    i.delay_init        delay.o
    0x08002ee4   0x08002ee4   0x0000003c   Code   RO         3511    i.delay_ms          delay.o
    0x08002f20   0x08002f20   0x0000003c   Code   RO         3512    i.delay_us          delay.o
    0x08002f5c   0x08002f5c   0x0000003c   Code   RO         4171    i.dmp_enable_6x_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08002f98   0x08002f98   0x0000021c   Code   RO         4172    i.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x080031b4   0x080031b4   0x00000058   Code   RO         4173    i.dmp_enable_gyro_cal  inv_mpu_dmp_motion_driver.o
    0x0800320c   0x0800320c   0x0000003c   Code   RO         4174    i.dmp_enable_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08003248   0x08003248   0x00000018   Code   RO         4179    i.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x08003260   0x08003260   0x000001c8   Code   RO         4180    i.dmp_read_fifo     inv_mpu_dmp_motion_driver.o
    0x08003428   0x08003428   0x00000130   Code   RO         4183    i.dmp_set_accel_bias  inv_mpu_dmp_motion_driver.o
    0x08003558   0x08003558   0x00000070   Code   RO         4184    i.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x080035c8   0x080035c8   0x00000130   Code   RO         4185    i.dmp_set_gyro_bias  inv_mpu_dmp_motion_driver.o
    0x080036f8   0x080036f8   0x00000138   Code   RO         4187    i.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x08003830   0x08003830   0x00000038   Code   RO         4190    i.dmp_set_shake_reject_thresh  inv_mpu_dmp_motion_driver.o
    0x08003868   0x08003868   0x00000026   Code   RO         4191    i.dmp_set_shake_reject_time  inv_mpu_dmp_motion_driver.o
    0x0800388e   0x0800388e   0x00000026   Code   RO         4192    i.dmp_set_shake_reject_timeout  inv_mpu_dmp_motion_driver.o
    0x080038b4   0x080038b4   0x00000046   Code   RO         4193    i.dmp_set_tap_axes  inv_mpu_dmp_motion_driver.o
    0x080038fa   0x080038fa   0x00000026   Code   RO         4194    i.dmp_set_tap_count  inv_mpu_dmp_motion_driver.o
    0x08003920   0x08003920   0x000001a0   Code   RO         4195    i.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x08003ac0   0x08003ac0   0x00000026   Code   RO         4196    i.dmp_set_tap_time  inv_mpu_dmp_motion_driver.o
    0x08003ae6   0x08003ae6   0x00000026   Code   RO         4197    i.dmp_set_tap_time_multi  inv_mpu_dmp_motion_driver.o
    0x08003b0c   0x08003b0c   0x00000004   Code   RO         3687    i.float_to_bytes    bsp_motor_iic.o
    0x08003b10   0x08003b10   0x000000b4   Code   RO         3834    i.get_accel_prod_shift  inv_mpu.o
    0x08003bc4   0x08003bc4   0x00000470   Code   RO         3836    i.get_st_biases     inv_mpu.o
    0x08004034   0x08004034   0x00000110   Code   RO         3837    i.gyro_self_test    inv_mpu.o
    0x08004144   0x08004144   0x00000060   Code   RO         3772    i.i2cWrite          ioi2c.o
    0x080041a4   0x080041a4   0x00000028   Code   RO         3838    i.inv_orientation_matrix_to_scalar  inv_mpu.o
    0x080041cc   0x080041cc   0x0000004e   Code   RO         3839    i.inv_row_2_scale   inv_mpu.o
    0x0800421a   0x0800421a   0x00000002   PAD
    0x0800421c   0x0800421c   0x000002bc   Code   RO           13    i.main              main.o
    0x080044d8   0x080044d8   0x0000005c   Code   RO         4354    i.mpu6050_read      mpu6050.o
    0x08004534   0x08004534   0x00000016   Code   RO         4355    i.mpu6050_read_reg  mpu6050.o
    0x0800454a   0x0800454a   0x00000044   Code   RO         4356    i.mpu6050_write     mpu6050.o
    0x0800458e   0x0800458e   0x00000012   Code   RO         4357    i.mpu6050_write_reg  mpu6050.o
    0x080045a0   0x080045a0   0x00000070   Code   RO         3840    i.mpu_configure_fifo  inv_mpu.o
    0x08004610   0x08004610   0x0000004c   Code   RO         3841    i.mpu_get_accel_fsr  inv_mpu.o
    0x0800465c   0x0800465c   0x00000054   Code   RO         3843    i.mpu_get_accel_sens  inv_mpu.o
    0x080046b0   0x080046b0   0x00000010   Code   RO         3848    i.mpu_get_fifo_config  inv_mpu.o
    0x080046c0   0x080046c0   0x00000044   Code   RO         3849    i.mpu_get_gyro_fsr  inv_mpu.o
    0x08004704   0x08004704   0x00000050   Code   RO         3851    i.mpu_get_gyro_sens  inv_mpu.o
    0x08004754   0x08004754   0x00000050   Code   RO         3853    i.mpu_get_lpf       inv_mpu.o
    0x080047a4   0x080047a4   0x00000020   Code   RO         3855    i.mpu_get_sample_rate  inv_mpu.o
    0x080047c4   0x080047c4   0x00000190   Code   RO         3857    i.mpu_init          inv_mpu.o
    0x08004954   0x08004954   0x000000b8   Code   RO         3858    i.mpu_load_firmware  inv_mpu.o
    0x08004a0c   0x08004a0c   0x000000e0   Code   RO         3859    i.mpu_lp_accel_mode  inv_mpu.o
    0x08004aec   0x08004aec   0x000000c0   Code   RO         3862    i.mpu_read_fifo_stream  inv_mpu.o
    0x08004bac   0x08004bac   0x00000080   Code   RO         3863    i.mpu_read_mem      inv_mpu.o
    0x08004c2c   0x08004c2c   0x000001c8   Code   RO         3866    i.mpu_reset_fifo    inv_mpu.o
    0x08004df4   0x08004df4   0x0000011c   Code   RO         3867    i.mpu_run_self_test  inv_mpu.o
    0x08004f10   0x08004f10   0x00000084   Code   RO         3869    i.mpu_set_accel_fsr  inv_mpu.o
    0x08004f94   0x08004f94   0x0000014c   Code   RO         3870    i.mpu_set_bypass    inv_mpu.o
    0x080050e0   0x080050e0   0x00000090   Code   RO         3872    i.mpu_set_dmp_state  inv_mpu.o
    0x08005170   0x08005170   0x00000088   Code   RO         3873    i.mpu_set_gyro_fsr  inv_mpu.o
    0x080051f8   0x080051f8   0x0000006c   Code   RO         3874    i.mpu_set_int_latched  inv_mpu.o
    0x08005264   0x08005264   0x00000084   Code   RO         3876    i.mpu_set_lpf       inv_mpu.o
    0x080052e8   0x080052e8   0x0000009c   Code   RO         3877    i.mpu_set_sample_rate  inv_mpu.o
    0x08005384   0x08005384   0x000000d0   Code   RO         3878    i.mpu_set_sensors   inv_mpu.o
    0x08005454   0x08005454   0x00000080   Code   RO         3879    i.mpu_write_mem     inv_mpu.o
    0x080054d4   0x080054d4   0x00000094   Code   RO         3880    i.run_self_test     inv_mpu.o
    0x08005568   0x08005568   0x00000090   Code   RO         3881    i.set_int_enable    inv_mpu.o
    0x080055f8   0x080055f8   0x0000004c   Code   RO         4616    i.sqrt              m_ws.l(sqrt.o)
    0x08005644   0x08005644   0x000000a0   Code   RO         3578    i.uart_init         usart.o
    0x080056e4   0x080056e4   0x000005f0   Data   RO          399    .constdata          oled.o
    0x08005cd4   0x08005cd4   0x00000050   Data   RO         3882    .constdata          inv_mpu.o
    0x08005d24   0x08005d24   0x00000bf6   Data   RO         4199    .constdata          inv_mpu_dmp_motion_driver.o
    0x0800691a   0x0800691a   0x00000006   PAD
    0x08006920   0x08006920   0x00000050   Data   RO         4501    .constdata          m_ws.l(asin.o)
    0x08006970   0x08006970   0x00000098   Data   RO         4588    .constdata          m_ws.l(atan.o)
    0x08006a08   0x08006a08   0x00000008   Data   RO         4614    .constdata          m_ws.l(qnan.o)
    0x08006a10   0x08006a10   0x00000020   Data   RO         4669    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006a30, Size: 0x00000528, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08006a30   0x0000004e   Data   RW           15    .data               main.o
    0x2000004e   0x08006a7e   0x00000002   PAD
    0x20000050   0x08006a80   0x00000014   Data   RW          495    .data               system_stm32f10x.o
    0x20000064   0x08006a94   0x00000014   Data   RW         2140    .data               stm32f10x_rcc.o
    0x20000078   0x08006aa8   0x00000004   Data   RW         3513    .data               delay.o
    0x2000007c   0x08006aac   0x00000008   Data   RW         3633    .data               bsp_timer.o
    0x20000084   0x08006ab4   0x00000020   Data   RW         3689    .data               bsp_motor_iic.o
    0x200000a4   0x08006ad4   0x00000035   Data   RW         3883    .data               inv_mpu.o
    0x200000d9   0x08006b09   0x00000003   PAD
    0x200000dc   0x08006b0c   0x00000004   Data   RW         4652    .data               mc_w.l(errno.o)
    0x200000e0        -       0x00000034   Zero   RW           14    .bss                main.o
    0x20000114        -       0x00000010   Zero   RW         4198    .bss                inv_mpu_dmp_motion_driver.o
    0x20000124   0x08006b10   0x00000004   PAD
    0x20000128        -       0x00000400   Zero   RW          536    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       122         18          0          0          0       1393   bsp.o
       312         40          0         32          0       6408   bsp_motor_iic.o
       280         32          0          8          0       2224   bsp_timer.o
         0          0          0          0          0         32   core_cm3.o
       196         24          0          4          0       1719   delay.o
      6686        214         80         53          0      30278   inv_mpu.o
      3092        124       3062          0         16      20378   inv_mpu_dmp_motion_driver.o
       476         48          0          0          0       3301   ioi2c.o
      2180        358          0         78         52     249984   main.o
       152         12          0          0          0       1478   misc.o
       626          0          0          0          0       7241   mpu6050.o
       540         74          0          0          0       5016   mpu6050_i2c.o
       844         26       1520          0          0       7471   oled.o
        36          8        304          0       1024        788   startup_stm32f10x_hd.o
       288          0          0          0          0       2476   stm32f10x_gpio.o
        26          0          0          0          0     250546   stm32f10x_it.o
       276         32          0         20          0       4346   stm32f10x_rcc.o
       252         42          0          0          0       3958   stm32f10x_tim.o
       442          6          0          0          0       6013   stm32f10x_usart.o
       176         28          0          0          0       1167   sys.o
       328         28          0         20          0       2181   system_stm32f10x.o
       228         16          0          0          0       1673   usart.o

    ----------------------------------------------------------------------
     17578       <USER>       <GROUP>        220       1096     610071   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          6          5          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       624         52         80          0          0        168   asin.o
       544         70        152          0          0        124   atan.o
       384         38          0          0          0        144   atan2.o
        38          6          0          0          0        272   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o
        76          0          0          0          0         84   sqrt.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        98          0          0          0          0         84   ldiv.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0        108   memseta.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        46          0          0          0          0         80   dscalb.o
       162          0          0          0          0        100   dsqrt.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        40          0          0          0          0         68   ffixui.o
        18          0          0          0          0         68   fflti.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      4362        <USER>        <GROUP>          4          0       3540   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1876        166        240          0          0        956   m_ws.l
       454         22          0          4          0        704   mc_w.l
      2022          0          0          0          0       1880   mf_w.l

    ----------------------------------------------------------------------
      4362        <USER>        <GROUP>          4          0       3540   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     21940       1318       5244        224       1096     603987   Grand Totals
     21940       1318       5244        224       1096     603987   ELF Image Totals
     21940       1318       5244        224          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                27184 (  26.55kB)
    Total RW  Size (RW Data + ZI Data)              1320 (   1.29kB)
    Total ROM Size (Code + RO Data + RW Data)      27408 (  26.77kB)

==============================================================================

