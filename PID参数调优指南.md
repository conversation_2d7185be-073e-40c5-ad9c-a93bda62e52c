# PID参数调优指南 - 解决超调和震荡问题

## 🎯 当前优化后的参数设置

### 新的PID参数（已应用）
```c
Kp = 0.8f;   // 降低比例系数，减少超调
Ki = 0.02f;  // 大幅降低积分系数，减少震荡
Kd = 1.2f;   // 增加微分系数，增强阻尼
最大输出 = 200;   // 降低最大速度
积分限幅 = 50;    // 减少积分限幅
```

### 控制改进
- **平滑滤波**：避免输出突变
- **动态限速**：接近目标时自动降速
- **积分分离**：大误差时不累积积分
- **稳定判断**：连续稳定50ms才认为到达

## 📊 OLED显示信息解读

### 显示布局
```
行1: 系统状态信息
行2: [当前角度]     [状态:IDLE/TURN/DONE]
行3: [目标角度]     [PID输出值]
行4: [角度差]       [积分项值]
```

### 关键数值的正常范围

#### 1. 当前角度 (行2左侧)
- **正常范围**: -180° 到 +180°
- **观察要点**: 手动旋转小车时应平滑变化
- **异常表现**: 数值跳跃、不变化或超出范围

#### 2. 角度差 (行4左侧)
- **旋转开始时**: ±90° (取决于旋转方向)
- **旋转过程中**: 逐渐减小到0°
- **到达目标时**: ±2°以内
- **异常表现**: 不减小、反向增大或震荡

#### 3. PID输出值 (行3右侧)
- **正常范围**: ±40 到 ±150
- **开始时**: 较大值(±100-150)
- **接近目标**: 较小值(±40-80)
- **到达时**: 接近0
- **异常表现**: 持续大值、频繁跳跃或为0

#### 4. 积分项值 (行4右侧)
- **正常范围**: ±10 到 ±30
- **稳态时**: 接近0
- **异常表现**: 持续增大、超过±50

## 🔧 问题诊断和解决

### 问题1: 仍有超调现象
**症状**: 角度差从90°快速减小，越过0°到负值
**解决方案**:
```c
// 进一步降低Kp值
PID_Init(&yaw_pid, 0.6f, 0.02f, 1.2f, 200.0f, 50.0f);
```

### 问题2: 响应太慢
**症状**: 角度差减小很慢，旋转速度过慢
**解决方案**:
```c
// 适当增加Kp值
PID_Init(&yaw_pid, 1.0f, 0.02f, 1.2f, 200.0f, 50.0f);
```

### 问题3: 仍有震荡
**症状**: 角度差在目标附近来回跳跃
**解决方案**:
```c
// 增加Kd值，降低Ki值
PID_Init(&yaw_pid, 0.8f, 0.01f, 1.5f, 200.0f, 50.0f);
```

### 问题4: 电机锁定
**症状**: 停止后电机无法手动转动
**原因**: control_speed函数在停止时仍发送控制信号
**解决方案**: 已在代码中修复，确保停止时发送0速度

## 🚀 逐步调优流程

### 第一步: 验证基础功能
1. 观察当前角度是否正常变化
2. 确认旋转状态正确切换
3. 检查PID输出是否合理

### 第二步: 粗调Kp参数
```c
// 从小值开始，逐渐增加
float kp_test[] = {0.5f, 0.8f, 1.0f, 1.2f};
// 选择响应快但不超调的值
```

### 第三步: 细调Kd参数
```c
// 在Kp基础上调整Kd
float kd_test[] = {0.8f, 1.2f, 1.5f, 2.0f};
// 选择震荡最小的值
```

### 第四步: 最后调Ki参数
```c
// 最后添加少量Ki消除稳态误差
float ki_test[] = {0.01f, 0.02f, 0.03f};
// 选择稳态误差最小且不震荡的值
```

## 📋 测试检查清单

### ✅ 基础检查
- [ ] 编译无错误
- [ ] 所有初始化信息正常显示
- [ ] MPU6050角度数据正常变化
- [ ] 旋转状态正确切换

### ✅ 性能检查
- [ ] 旋转开始响应及时（<0.5秒）
- [ ] 无明显超调（角度差不越过-10°）
- [ ] 无持续震荡（±5°范围内稳定）
- [ ] 到达精度良好（±3°以内）
- [ ] 停止后电机可手动转动

### ✅ 稳定性检查
- [ ] 连续10次旋转结果一致
- [ ] 不同起始角度下表现稳定
- [ ] 左转右转性能对称
- [ ] 无异常停止或卡死现象

## 🔄 备用参数组合

如果当前参数仍不理想，可尝试以下组合：

### 保守型（稳定优先）
```c
PID_Init(&yaw_pid, 0.6f, 0.01f, 1.5f, 150.0f, 30.0f);
```

### 平衡型（推荐）
```c
PID_Init(&yaw_pid, 0.8f, 0.02f, 1.2f, 200.0f, 50.0f);
```

### 激进型（速度优先）
```c
PID_Init(&yaw_pid, 1.0f, 0.03f, 1.0f, 250.0f, 60.0f);
```

## 📞 故障排除联系

如果问题仍然存在，请提供以下信息：
1. OLED显示的具体数值
2. 旋转过程的详细描述
3. 当前使用的参数组合
4. 测试环境（地面类型、电池电压等）
