#include "AllHeader.h"

static u32 tim1_counter = 0;//定时器1计数器

#define UPLOAD_DATA 2  //1:接收总的编码器数据 2:接收实时的编码器数据

#define MOTOR_TYPE 5   //1:520电机 2:310电机 3:编码器盘TT电机 4:TT直流减速电机 5:L型520电机

uint8_t times = 0;
static uint32_t last_tim1_counter = 0;  // 上次定时器1计数值
static uint8_t led_state = 0;           // LED状态标志

float Pitch,Roll,Yaw;			//俯仰角默认跟中值一样，翻滚角，偏航角
int16_t ax,ay,az,gx,gy,gz;		//加速度，陀螺仪角速度

u8 MPU_Get_Gyroscope(short *gx,short *gy,short *gz);
u8 MPU_Get_Accelerometer(short *ax,short *ay,short *az);

// PID控制器结构体
typedef struct {
    float kp;           // 比例系数
    float ki;           // 积分系数
    float kd;           // 微分系数
    float target;       // 目标值
    float current;      // 当前值
    float error;        // 当前误差
    float last_error;   // 上次误差
    float integral;     // 积分累积
    float derivative;   // 微分
    float output;       // 输出值
    float max_output;   // 最大输出限制
    float min_output;   // 最小输出限制
    float max_integral; // 积分限幅
} PID_Controller;

// 旋转控制状态枚举
typedef enum {
    ROTATION_IDLE = 0,      // 空闲状态
    ROTATION_TURNING,       // 正在旋转
    ROTATION_COMPLETE       // 旋转完成
} RotationState;

// 全局变量
PID_Controller yaw_pid;         // Yaw轴PID控制器
RotationState rotation_state = ROTATION_IDLE;   // 旋转状态
float target_yaw = 0.0f;        // 目标偏航角
float initial_yaw = 0.0f;       // 初始偏航角
uint32_t rotation_timeout = 0;  // 旋转超时计数器
uint8_t rotation_direction = 0; // 旋转方向：1=右转，2=左转

// PID控制器初始化
void PID_Init(PID_Controller *pid, float kp, float ki, float kd, float max_out, float max_integral) {
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->target = 0.0f;
    pid->current = 0.0f;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
    pid->max_output = max_out;
    pid->min_output = -max_out;
    pid->max_integral = max_integral;
}

// PID计算函数
float PID_Calculate(PID_Controller *pid, float current_value) {
    pid->current = current_value;
    pid->error = pid->target - pid->current;

    // 积分项计算（带限幅）
    pid->integral += pid->error;
    if (pid->integral > pid->max_integral) {
        pid->integral = pid->max_integral;
    } else if (pid->integral < -pid->max_integral) {
        pid->integral = -pid->max_integral;
    }

    // 微分项计算
    pid->derivative = pid->error - pid->last_error;

    // PID输出计算
    pid->output = pid->kp * pid->error + pid->ki * pid->integral + pid->kd * pid->derivative;

    // 输出限幅
    if (pid->output > pid->max_output) {
        pid->output = pid->max_output;
    } else if (pid->output < pid->min_output) {
        pid->output = pid->min_output;
    }

    pid->last_error = pid->error;
    return pid->output;
}

// 角度差计算函数（处理-180到180度的循环）
float AngleDifference(float target, float current) {
    float diff = target - current;
    while (diff > 180.0f) diff -= 360.0f;
    while (diff < -180.0f) diff += 360.0f;
    return diff;
}

// 开始旋转函数
void StartRotation(uint8_t direction) {
    if (rotation_state != ROTATION_IDLE) return;  // 如果正在旋转，忽略新的旋转命令

    rotation_direction = direction;
    initial_yaw = Yaw;

    if (direction == 1) {  // 右转90度
        target_yaw = initial_yaw + 90.0f;
        if (target_yaw > 180.0f) target_yaw -= 360.0f;
    } else if (direction == 2) {  // 左转90度
        target_yaw = initial_yaw - 90.0f;
        if (target_yaw < -180.0f) target_yaw += 360.0f;
    }

    yaw_pid.target = target_yaw;
    yaw_pid.integral = 0.0f;  // 清除积分项
    rotation_state = ROTATION_TURNING;
    rotation_timeout = 5000;  // 5秒超时（5000ms）
}

// 自定义角度旋转函数
void StartRotationAngle(float angle) {
    if (rotation_state != ROTATION_IDLE) return;

    initial_yaw = Yaw;
    target_yaw = initial_yaw + angle;

    // 角度归一化到-180到180度范围
    while (target_yaw > 180.0f) target_yaw -= 360.0f;
    while (target_yaw < -180.0f) target_yaw += 360.0f;

    yaw_pid.target = target_yaw;
    yaw_pid.integral = 0.0f;
    rotation_state = ROTATION_TURNING;
    rotation_timeout = 8000;  // 8秒超时
}

// 停止旋转函数
void StopRotation(void) {
    rotation_state = ROTATION_IDLE;
    control_speed(0, 0, 0, 0);
}

// 检查旋转是否完成
uint8_t IsRotationComplete(void) {
    return (rotation_state == ROTATION_COMPLETE || rotation_state == ROTATION_IDLE);
}

// 获取当前旋转状态
RotationState GetRotationState(void) {
    return rotation_state;
}



void Car_Move(void)
{
	static uint8_t state = 0;
	switch(state)
	{
		case 0:
		control_speed(0,200,0,200);//前进 Forward (降低速度)
		OLED_ShowString(1, 10, "FWD");
		break;

		case 1:
		StartRotation(1);  // 右转90度
		OLED_ShowString(1, 10, "R90");
		break;

		case 2:
		control_speed(0,200,0,200);//前进 Forward
		OLED_ShowString(1, 10, "FWD");
		break;

		case 3:
		StartRotation(2);  // 左转90度
		OLED_ShowString(1, 10, "L90");
		break;

		case 4:
		control_speed(0,0,0,0);//停止 Stop
		OLED_ShowString(1, 10, "STP");
		break;
	}
	state++;
	if(state>4)state=0;
}

// 简单的旋转测试函数
void TestRotation(void) {
    static uint8_t test_state = 0;
    static uint32_t test_timer = 0;

    test_timer++;

    switch(test_state) {
        case 0:
            if (test_timer > 3000) {  // 等待3秒
                StartRotation(1);  // 右转90度
                test_state = 1;
                test_timer = 0;
            }
            break;

        case 1:
            if (IsRotationComplete()) {
                test_state = 2;
                test_timer = 0;
            }
            break;

        case 2:
            if (test_timer > 2000) {  // 等待2秒
                StartRotation(2);  // 左转90度
                test_state = 3;
                test_timer = 0;
            }
            break;

        case 3:
            if (IsRotationComplete()) {
                test_state = 0;  // 重新开始循环
                test_timer = 0;
            }
            break;
    }
}




int main(void)
{
	// 基础系统初始化
	bsp_init();
	delay_ms(100);

	// OLED初始化
	OLED_Init();
	delay_ms(100);
	OLED_ShowString(1, 1, "OLED OK");
	delay_ms(500);

	// MPU6050初始化
	MPU6050_Init();
	MPU6050_DMP_Init();
	OLED_ShowString(2, 1, "MPU6050 OK");
	delay_ms(500);

	// 定时器初始化
	TIM1_Init();  // 初始化定时器1，1ms定时中断
	TIM3_Init();
	OLED_ShowString(3, 1, "Timer OK");
	delay_ms(500);

	//电机模块iic通信初始化
	IIC_Motor_Init();
	OLED_ShowString(4, 1, "Motor Init");
	delay_ms(500);

    control_pwm(0,0,0,0);
    delay_ms(100);

	// PID控制器初始化
	// 参数：Kp=2.0, Ki=0.1, Kd=0.5, 最大输出=300, 积分限幅=100
	PID_Init(&yaw_pid, 2.0f, 0.1f, 0.5f, 300.0f, 100.0f);
	OLED_ShowString(1, 1, "PID Init");
	delay_ms(500);

    #if MOTOR_TYPE == 5
	Set_motor_type(1);//设置电机类型
	delay_ms(100);
	Set_Pluse_Phase(40);//设置减速比 查看电机手册得出
	delay_ms(100);
	Set_Pluse_line(11);//设置磁环线数 查看电机手册得出
	delay_ms(100);
	Set_Wheel_dis(67.00);//设置车轮直径,实测得出
	delay_ms(100);
	Set_motor_deadzone(1900);//设置电机死区,实验得出
	delay_ms(100);
    #endif

	// 测试模式选择：0=原始移动模式，1=纯旋转测试模式
	uint8_t test_mode = 1;  // 改为1来测试纯旋转功能

	while(1)
	{
		//OLED显示姿态角
		MPU6050_DMP_Get_Data(&Pitch,&Roll,&Yaw);  //读取姿态信息(其中偏航角有漂移是正常现象)
		MPU_Get_Gyroscope(&gx,&gy,&gz);
		MPU_Get_Accelerometer(&ax,&ay,&az);

		// 显示当前角度和目标角度
		OLED_ShowSignedNum(2, 1, (int)Yaw, 4);
		OLED_ShowSignedNum(3, 1, (int)target_yaw, 4);
		OLED_ShowSignedNum(4, 1, (int)(yaw_pid.output), 4);

		// 显示旋转状态
		if (rotation_state == ROTATION_IDLE) {
			OLED_ShowString(2, 8, "IDLE");
		} else if (rotation_state == ROTATION_TURNING) {
			OLED_ShowString(2, 8, "TURN");
		} else {
			OLED_ShowString(2, 8, "DONE");
		}

		if (test_mode == 0) {
			// 原始移动模式：前进+旋转组合
			if(times>=250 && rotation_state == ROTATION_IDLE) {
				Car_Move();
				times = 0;
			}
		} else {
			// 纯旋转测试模式：只测试旋转功能
			TestRotation();
		}
	}
}



// PID旋转控制处理函数
void ProcessRotationControl(void) {
    if (rotation_state != ROTATION_TURNING) return;

    // 计算角度差
    float angle_diff = AngleDifference(target_yaw, Yaw);

    // 检查是否到达目标角度（误差小于2度认为到达）
    if (fabs(angle_diff) < 2.0f) {
        rotation_state = ROTATION_COMPLETE;
        control_speed(0, 0, 0, 0);  // 停止电机
        return;
    }

    // 检查超时
    if (rotation_timeout == 0) {
        rotation_state = ROTATION_COMPLETE;
        control_speed(0, 0, 0, 0);  // 停止电机
        return;
    }
    rotation_timeout--;

    // PID计算
    float pid_output = PID_Calculate(&yaw_pid, Yaw);

    // 将PID输出转换为电机速度
    int16_t motor_speed = (int16_t)pid_output;

    // 限制电机速度范围
    if (motor_speed > 200) motor_speed = 200;
    if (motor_speed < -200) motor_speed = -200;

    // 根据PID输出控制电机旋转
    // 左轮和右轮反向旋转实现原地旋转
    control_speed(0, -motor_speed, 0, motor_speed);
}

void TIM1_UP_IRQHandler(void)
{
	if (TIM_GetITStatus(TIM1, TIM_IT_Update) != RESET) // 检查TIM1更新中断发生与否
	{
		TIM_ClearITPendingBit(TIM1, TIM_IT_Update);    // 清除TIM1更新中断标志

		// 计数器递增
		tim1_counter++;

		// PID旋转控制处理（每1ms执行一次）
		ProcessRotationControl();

		// 防止计数器溢出
		if (tim1_counter >= 0xFFFFFFFF)
		{
			tim1_counter = 0;
		}
	}
}