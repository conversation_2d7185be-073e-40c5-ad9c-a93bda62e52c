#include "AllHeader.h"

static u32 tim1_counter = 0;//定时器1计数器 

#define UPLOAD_DATA 2  //1:接收总的编码器数据 2:接收实时的编码器数据

#define MOTOR_TYPE 5   //1:520电机 2:310电机 3:编码器盘TT电机 4:TT直流减速电机 5:L型520电机

uint8_t times = 0;
static uint32_t last_tim1_counter = 0;  // 上次定时器1计数值
static uint8_t led_state = 0;           // LED状态标志

float Pitch,Roll,Yaw;			//俯仰角默认跟中值一样，翻滚角，偏航角
int16_t ax,ay,az,gx,gy,gz;		//加速度，陀螺仪角速度

u8 MPU_Get_Gyroscope(short *gx,short *gy,short *gz);
u8 MPU_Get_Accelerometer(short *ax,short *ay,short *az);



void Car_Move(void)
{
	static uint8_t state = 0;
	switch(state)
	{
		case 0:
		control_speed(0,300,0,300);//前进 Forward
		OLED_ShowString(1, 10, "FWD");
		break;

		case 1:
		control_speed(0,300,0,300);//前进 Forward
		OLED_ShowString(1, 10, "FWD");
		break;

		case 2:
		control_speed(0,-300,0,-300);//后退 Back
		OLED_ShowString(1, 10, "BCK");
		break;

		case 3:
		control_speed(0,-300,0,-300);//后退 Back
		OLED_ShowString(1, 10, "BCK");
		break;

		case 4:
		control_speed(0,0,0,0);//停止 Stop
		OLED_ShowString(1, 10, "STP");
		break;
	}
	state++;
	if(state>4)state=0;
}




int main(void)
{
	// 基础系统初始化
	bsp_init();
	delay_ms(100);

	// OLED初始化
	OLED_Init();
	delay_ms(100);
	OLED_ShowString(1, 1, "OLED OK");
	delay_ms(500);

	// MPU6050初始化
	MPU6050_Init();
	MPU6050_DMP_Init();
	OLED_ShowString(2, 1, "MPU6050 OK");
	delay_ms(500);

	// 定时器初始化
	TIM1_Init();  // 初始化定时器1，1ms定时中断
	TIM3_Init();
	OLED_ShowString(3, 1, "Timer OK");
	delay_ms(500);

	//电机模块iic通信初始化
	IIC_Motor_Init();
	OLED_ShowString(4, 1, "Motor Init");
	delay_ms(500);

    control_pwm(0,0,0,0);
    delay_ms(100);


    #if MOTOR_TYPE == 5
	Set_motor_type(1);//设置电机类型	
	delay_ms(100);
	Set_Pluse_Phase(40);//设置减速比 查看电机手册得出	
	delay_ms(100);
	Set_Pluse_line(11);//设置磁环线数 查看电机手册得出	
	delay_ms(100);
	Set_Wheel_dis(67.00);//设置车轮直径,实测得出		
	delay_ms(100);
	Set_motor_deadzone(1900);//设置电机死区,实验得出	
	delay_ms(100);
    #endif

	while(1)
	{
		//OLED显示姿态角
		MPU6050_DMP_Get_Data(&Pitch,&Roll,&Yaw);  //读取姿态信息(其中偏航角有漂移是正常现象)
		MPU_Get_Gyroscope(&gx,&gy,&gz);
		MPU_Get_Accelerometer(&ax,&ay,&az);

		OLED_ShowSignedNum(2, 1, Pitch, 5);
		OLED_ShowSignedNum(3, 1, Roll, 5);
		OLED_ShowSignedNum(4, 1, Yaw, 5);
		OLED_ShowSignedNum(2, 8, gx, 5);
		OLED_ShowSignedNum(3, 8, gy, 5);
		OLED_ShowSignedNum(4, 8, gz, 5);



		if(times>=250)//定时器每20ms累计一次，达到250次，即5秒时改变一次小车的状态
		{
			Car_Move();
			times = 0;
		}
	}
}



void TIM1_UP_IRQHandler(void)
{
	if (TIM_GetITStatus(TIM1, TIM_IT_Update) != RESET) // 检查TIM1更新中断发生与否  
	{
		TIM_ClearITPendingBit(TIM1, TIM_IT_Update);    // 清除TIM1更新中断标志 

		// 计数器递增  
		tim1_counter++;

		// 在这里添加您需要每1ms执行的代码  
		
		
		
		
		// 防止计数器溢出
		if (tim1_counter >= 0xFFFFFFFF)
		{
			tim1_counter = 0;
		}
	}
}