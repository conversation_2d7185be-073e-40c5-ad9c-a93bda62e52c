/*
 * 电机测试程序 - 用于验证电机控制是否正常
 * 将此代码添加到main函数中进行测试
 */

#include "AllHeader.h"

// 简单的电机测试函数
void TestMotorBasic(void) {
    // 测试1：前进
    OLED_ShowString(1, 1, "Test FWD");
    control_speed(0, 100, 0, 100);
    delay_ms(2000);
    control_speed(0, 0, 0, 0);
    delay_ms(1000);
    
    // 测试2：后退
    OLED_ShowString(1, 1, "Test BCK");
    control_speed(0, -100, 0, -100);
    delay_ms(2000);
    control_speed(0, 0, 0, 0);
    delay_ms(1000);
    
    // 测试3：右转
    OLED_ShowString(1, 1, "Test R");
    control_speed(0, -100, 0, 100);
    delay_ms(2000);
    control_speed(0, 0, 0, 0);
    delay_ms(1000);
    
    // 测试4：左转
    OLED_ShowString(1, 1, "Test L");
    control_speed(0, 100, 0, -100);
    delay_ms(2000);
    control_speed(0, 0, 0, 0);
    delay_ms(1000);
}

// 渐进式电机测试
void TestMotorProgressive(void) {
    int16_t speeds[] = {50, 100, 150, 200, 250};
    int num_speeds = sizeof(speeds) / sizeof(speeds[0]);
    
    for (int i = 0; i < num_speeds; i++) {
        OLED_ShowString(1, 1, "Test Speed");
        OLED_ShowSignedNum(1, 12, speeds[i], 3);
        
        // 右转测试
        control_speed(0, -speeds[i], 0, speeds[i]);
        delay_ms(1000);
        control_speed(0, 0, 0, 0);
        delay_ms(1000);
    }
}

// MPU6050数据测试
void TestMPU6050Data(void) {
    for (int i = 0; i < 100; i++) {  // 测试100次
        MPU6050_DMP_Get_Data(&Pitch, &Roll, &Yaw);
        
        OLED_ShowSignedNum(2, 1, (int)Pitch, 4);
        OLED_ShowSignedNum(3, 1, (int)Roll, 4);
        OLED_ShowSignedNum(4, 1, (int)Yaw, 4);
        
        delay_ms(100);
    }
}

// PID计算测试
void TestPIDCalculation(void) {
    // 初始化PID
    PID_Init(&yaw_pid, 2.0f, 0.1f, 0.5f, 300.0f, 100.0f);
    
    // 模拟不同的角度差进行测试
    float test_angles[] = {10.0f, 30.0f, 60.0f, 90.0f, -10.0f, -30.0f, -60.0f, -90.0f};
    int num_tests = sizeof(test_angles) / sizeof(test_angles[0]);
    
    for (int i = 0; i < num_tests; i++) {
        yaw_pid.target = 0.0f;  // 目标是角度差为0
        float pid_output = PID_Calculate(&yaw_pid, test_angles[i]);
        
        OLED_ShowString(1, 1, "PID Test");
        OLED_ShowSignedNum(2, 1, (int)test_angles[i], 4);
        OLED_ShowSignedNum(3, 1, (int)pid_output, 4);
        
        delay_ms(2000);
    }
}

// 完整的旋转测试（手动版本）
void TestRotationManual(void) {
    // 读取初始角度
    MPU6050_DMP_Get_Data(&Pitch, &Roll, &Yaw);
    float initial_yaw = Yaw;
    float target_yaw = initial_yaw + 90.0f;
    
    // 角度归一化
    while (target_yaw > 180.0f) target_yaw -= 360.0f;
    while (target_yaw < -180.0f) target_yaw += 360.0f;
    
    OLED_ShowString(1, 1, "Manual Rot");
    OLED_ShowSignedNum(2, 1, (int)initial_yaw, 4);
    OLED_ShowSignedNum(3, 1, (int)target_yaw, 4);
    
    // 初始化PID
    PID_Init(&yaw_pid, 2.0f, 0.1f, 0.5f, 300.0f, 100.0f);
    yaw_pid.target = 0.0f;
    
    uint32_t start_time = TIM1_GetCounter();
    uint32_t timeout = 5000;  // 5秒超时
    
    while (timeout > 0) {
        // 读取当前角度
        MPU6050_DMP_Get_Data(&Pitch, &Roll, &Yaw);
        
        // 计算角度差
        float angle_diff = AngleDifference(target_yaw, Yaw);
        
        // 显示当前状态
        OLED_ShowSignedNum(4, 1, (int)angle_diff, 4);
        
        // 检查是否到达目标
        if (fabs(angle_diff) < 3.0f) {
            control_speed(0, 0, 0, 0);
            OLED_ShowString(1, 8, "DONE");
            break;
        }
        
        // PID计算
        float pid_output = PID_Calculate(&yaw_pid, angle_diff);
        
        // 转换为电机速度
        int16_t motor_speed = (int16_t)pid_output;
        if (motor_speed > 200) motor_speed = 200;
        if (motor_speed < -200) motor_speed = -200;
        
        // 死区补偿
        if (motor_speed > 0 && motor_speed < 50) motor_speed = 50;
        if (motor_speed < 0 && motor_speed > -50) motor_speed = -50;
        
        // 控制电机
        control_speed(0, -motor_speed, 0, motor_speed);
        
        // 显示PID输出
        OLED_ShowSignedNum(4, 8, motor_speed, 4);
        
        delay_ms(10);
        timeout--;
    }
    
    // 停止电机
    control_speed(0, 0, 0, 0);
    
    if (timeout == 0) {
        OLED_ShowString(1, 8, "TIMEOUT");
    }
    
    delay_ms(3000);
}

/*
 * 使用方法：
 * 在main函数的while(1)循环之前添加以下代码来测试：
 * 
 * // 选择测试模式
 * uint8_t test_mode = 1;
 * 
 * switch(test_mode) {
 *     case 1:
 *         TestMotorBasic();
 *         break;
 *     case 2:
 *         TestMotorProgressive();
 *         break;
 *     case 3:
 *         TestMPU6050Data();
 *         break;
 *     case 4:
 *         TestPIDCalculation();
 *         break;
 *     case 5:
 *         TestRotationManual();
 *         break;
 * }
 */
