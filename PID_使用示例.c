/*
 * PID旋转控制使用示例
 * 展示如何使用PID控制系统实现精确旋转
 */

#include "AllHeader.h"

// 示例1：简单的90度旋转测试
void Example_Simple90DegreeRotation(void) {
    // 等待系统稳定
    delay_ms(2000);
    
    // 右转90度
    StartRotation(1);
    
    // 等待旋转完成
    while (!IsRotationComplete()) {
        delay_ms(10);
    }
    
    // 等待2秒
    delay_ms(2000);
    
    // 左转90度
    StartRotation(2);
    
    // 等待旋转完成
    while (!IsRotationComplete()) {
        delay_ms(10);
    }
}

// 示例2：自定义角度旋转
void Example_CustomAngleRotation(void) {
    float angles[] = {45.0f, -30.0f, 180.0f, -90.0f};
    int num_angles = sizeof(angles) / sizeof(angles[0]);
    
    for (int i = 0; i < num_angles; i++) {
        // 开始旋转到指定角度
        StartRotationAngle(angles[i]);
        
        // 等待旋转完成
        while (!IsRotationComplete()) {
            delay_ms(10);
        }
        
        // 等待2秒再进行下一个旋转
        delay_ms(2000);
    }
}

// 示例3：带状态监控的旋转
void Example_RotationWithMonitoring(void) {
    StartRotation(1);  // 开始右转90度
    
    uint32_t start_time = TIM1_GetCounter();
    
    while (!IsRotationComplete()) {
        RotationState state = GetRotationState();
        uint32_t elapsed_time = TIM1_GetCounter() - start_time;
        
        // 每100ms输出一次状态信息
        if (elapsed_time % 100 == 0) {
            switch (state) {
                case ROTATION_IDLE:
                    // 不应该出现这种情况
                    break;
                case ROTATION_TURNING:
                    // 正在旋转，可以在这里添加监控代码
                    break;
                case ROTATION_COMPLETE:
                    // 旋转完成
                    break;
            }
        }
        
        delay_ms(1);
    }
}

// 示例4：连续旋转序列
void Example_RotationSequence(void) {
    // 定义旋转序列：右90° → 右90° → 左180° → 右90°
    uint8_t sequence[] = {1, 1, 2, 2, 1};  // 1=右转90°, 2=左转90°
    int sequence_length = sizeof(sequence) / sizeof(sequence[0]);
    
    for (int i = 0; i < sequence_length; i++) {
        StartRotation(sequence[i]);
        
        // 等待旋转完成
        while (!IsRotationComplete()) {
            delay_ms(10);
        }
        
        // 每次旋转后等待1秒
        delay_ms(1000);
    }
}

// 示例5：PID参数在线调整示例
void Example_PIDTuning(void) {
    // 这个示例展示如何在运行时调整PID参数
    
    // 测试不同的Kp值
    float kp_values[] = {1.0f, 2.0f, 3.0f};
    
    for (int i = 0; i < 3; i++) {
        // 重新初始化PID控制器
        PID_Init(&yaw_pid, kp_values[i], 0.1f, 0.5f, 300.0f, 100.0f);
        
        // 执行标准90度旋转测试
        StartRotation(1);
        
        uint32_t start_time = TIM1_GetCounter();
        
        while (!IsRotationComplete()) {
            delay_ms(10);
        }
        
        uint32_t rotation_time = TIM1_GetCounter() - start_time;
        
        // 在这里可以记录旋转时间和精度，用于参数优化
        // 实际应用中可以通过串口输出或OLED显示
        
        delay_ms(2000);  // 等待系统稳定
    }
}

// 示例6：错误处理和恢复
void Example_ErrorHandling(void) {
    StartRotation(1);
    
    uint32_t timeout_counter = 0;
    const uint32_t MAX_TIMEOUT = 10000;  // 10秒超时
    
    while (!IsRotationComplete()) {
        timeout_counter++;
        
        if (timeout_counter > MAX_TIMEOUT) {
            // 超时处理
            StopRotation();
            
            // 可以在这里添加错误恢复代码
            // 例如：重新初始化系统、记录错误日志等
            
            break;
        }
        
        delay_ms(1);
    }
}

// 主函数中的使用示例
void PID_Demo_Main(void) {
    // 系统初始化（在main函数中已完成）
    
    // 选择要运行的示例
    uint8_t demo_mode = 1;
    
    switch (demo_mode) {
        case 1:
            Example_Simple90DegreeRotation();
            break;
        case 2:
            Example_CustomAngleRotation();
            break;
        case 3:
            Example_RotationWithMonitoring();
            break;
        case 4:
            Example_RotationSequence();
            break;
        case 5:
            Example_PIDTuning();
            break;
        case 6:
            Example_ErrorHandling();
            break;
        default:
            // 默认运行简单测试
            Example_Simple90DegreeRotation();
            break;
    }
}

/*
 * 使用说明：
 * 1. 在main函数中调用PID_Demo_Main()来运行示例
 * 2. 修改demo_mode变量来选择不同的示例
 * 3. 根据实际需要修改PID参数和旋转角度
 * 4. 可以结合OLED显示和串口输出来监控系统状态
 */
