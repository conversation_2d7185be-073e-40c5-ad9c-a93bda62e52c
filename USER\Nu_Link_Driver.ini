[Version]
Nu_LinkVersion=V3.6
[Process]
ProcessID=0x00000000
ProcessCreationTime_L=0x00000000
ProcessCreationTime_H=0x00000000
NuLinkID=0x00000000
[ChipSelect]
;ChipName=<NUC1xx|NUC2xx|M05x|N571|N572|Nano100|N512|Mini51|NUC505|General>
ChipName=NUC1xx
[NUC505]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NUC505_SPIFLASH.FLM
[NUC4xx]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=NUC400_AP_512.FLM
[NUC2xx]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC200_AP_128.FLM
[NUC1xx]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NUC100_AP_128.FLM
[NUC029]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NUC029_AP_16.FLM
[NM1500]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=NM1500_AP_128.FLM
[NM1200]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=NM1200_AP_8.FLM
[Nano100]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=Nano100_AP_64.FLM
[N572]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=N572Fxxx.FLM
[N571]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x2000
ProgramAlgorithm=N571E000.FLM
[N512]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=N512_AP_64.FLM
[Mini51]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=Mini51_AP_16.FLM
[M451]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=0
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=M451_AP_256.FLM
[M0518]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=M0518_AP_64.FLM
[M05x]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=1
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x800
ProgramAlgorithm=M0516_AP_64.FLM
[ISD9300]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9300_AP_145.FLM 
[ISD9xxx]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=ISD9100_AP_145.FLM
[AU9xxx]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
FlashSelect=APROM
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableFlashBreakpoint=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x1000
ProgramAlgorithm=AU9100_AP_145.FLM
[General]
Reset=Autodetect
MaxClock=1MHz
MemoryVerify=1
IOVoltage=3300
Erase=1
Program=1
Verify=1
ResetAndRun=0
EnableLog=0
MemAccessWhileRun=0
RAMForAlgorithmStart=0x20000000
RAMForAlgorithmSize=0x4000
ProgramAlgorithm=