# PID旋转控制系统调试指南

## 🔍 问题诊断步骤

### 1. 基础系统检查

#### 1.1 编译检查
- ✅ 代码编译无错误
- ✅ 所有头文件正确包含

#### 1.2 硬件初始化检查
观察OLED显示的初始化信息：
- "OLED OK" - OLED显示正常
- "MPU6050 OK" - 陀螺仪初始化成功
- "Timer OK" - 定时器初始化成功
- "Motor Init" - 电机模块初始化成功
- "PID Init" - PID控制器初始化成功

### 2. MPU6050数据检查

#### 2.1 角度数据验证
观察OLED第2行显示的Yaw角度：
- 数值应该在-180到180之间
- 手动旋转小车，数值应该相应变化
- 如果数值不变或异常，检查MPU6050连接

#### 2.2 角度稳定性测试
- 小车静止时，Yaw角度应该相对稳定
- 轻微漂移（1-2度/分钟）是正常的
- 如果漂移严重，可能需要重新校准MPU6050

### 3. PID控制系统检查

#### 3.1 旋转状态监控
观察OLED右侧显示的状态：
- "IDLE" - 系统空闲，等待旋转命令
- "TURN" - 正在旋转，PID控制器工作中
- "DONE" - 旋转完成

#### 3.2 PID输出检查
观察OLED第3行右侧的PID输出值：
- 旋转时应该有非零输出（通常在±50到±250之间）
- 如果输出为0，说明PID计算有问题
- 如果输出过大，可能需要调整PID参数

### 4. 电机控制检查

#### 4.1 电机响应测试
在main函数中添加简单的电机测试：
```c
// 临时测试代码，添加到main函数的while循环前
control_speed(0, 100, 0, -100);  // 测试旋转
delay_ms(2000);
control_speed(0, 0, 0, 0);       // 停止
delay_ms(2000);
```

#### 4.2 电机参数检查
确认以下参数设置正确：
- 电机类型：L型520电机
- 减速比：40
- 磁环线数：11
- 车轮直径：67.00mm
- 电机死区：1900

### 5. 定时器中断检查

#### 5.1 TIM1中断验证
在TIM1_UP_IRQHandler中添加LED闪烁来验证中断正常：
```c
static uint32_t led_counter = 0;
led_counter++;
if (led_counter >= 1000) {  // 每秒闪烁一次
    // 切换LED状态
    led_counter = 0;
}
```

## 🛠️ 常见问题及解决方案

### 问题1：电机不转动
**可能原因：**
- 电机死区设置不当
- PID输出值太小
- 电机驱动板故障
- 电源电压不足

**解决方案：**
1. 增加最小输出值（死区补偿）
2. 调整PID参数，增加Kp值
3. 检查电机连接和驱动板
4. 检查电池电压

### 问题2：旋转不准确
**可能原因：**
- PID参数不合适
- 角度计算错误
- 机械结构问题

**解决方案：**
1. 调整PID参数
2. 检查角度差计算逻辑
3. 检查车轮是否打滑

### 问题3：旋转震荡
**可能原因：**
- Kp值过大
- Kd值过小
- 机械振动

**解决方案：**
1. 降低Kp值
2. 增加Kd值
3. 检查机械结构稳定性

### 问题4：系统无响应
**可能原因：**
- 定时器中断未启动
- 旋转状态未正确设置
- MPU6050数据读取失败

**解决方案：**
1. 检查定时器初始化
2. 检查StartRotation函数调用
3. 检查MPU6050初始化

## 📊 调试参数建议

### 初始PID参数
```c
Kp = 2.0f;   // 比例系数
Ki = 0.1f;   // 积分系数
Kd = 0.5f;   // 微分系数
```

### 参数调整策略
1. **先调Kp**：从1.0开始，逐渐增加到系统开始震荡，然后减半
2. **再调Kd**：增加Kd值来减少超调和震荡
3. **最后调Ki**：添加少量Ki来消除稳态误差

### 电机参数
```c
最大输出 = 250;     // 电机最大速度
最小输出 = 50;      // 死区补偿
角度误差阈值 = 3.0f; // 到达判断阈值
```

## 🔧 调试工具

### OLED显示信息
- 第2行：当前Yaw角度
- 第3行：目标Yaw角度
- 第4行：角度差
- 右侧第2行：旋转状态
- 右侧第3行：PID输出值

### 测试模式
- test_mode = 0：完整移动序列
- test_mode = 1：循环旋转测试
- test_mode = 2：立即旋转测试

## 📝 调试记录模板

记录以下信息有助于问题诊断：
- 当前PID参数：Kp=___, Ki=___, Kd=___
- 旋转角度误差：目标___°，实际___°，误差___°
- 旋转时间：___秒
- PID输出范围：最小___，最大___
- 电机响应：正常/异常
- 其他观察：___________

## 🚀 优化建议

1. **逐步调试**：先确保基础功能正常，再优化精度
2. **参数记录**：记录每次调整的参数和效果
3. **环境测试**：在不同地面条件下测试
4. **电池监控**：确保电池电量充足
5. **机械检查**：定期检查车轮和传动系统
