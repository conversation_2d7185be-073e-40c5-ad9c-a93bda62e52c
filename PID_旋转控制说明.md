# PID旋转控制系统使用说明

## 功能概述
本系统基于MPU6050陀螺仪数据，使用PID算法控制小车精确旋转90°（或任意角度）。

## 主要特性
1. **精确角度控制**：使用PID算法实现精确的角度控制，误差小于2°
2. **自动角度归一化**：自动处理-180°到180°的角度循环
3. **超时保护**：防止系统卡死，超时自动停止
4. **状态监控**：实时显示旋转状态和PID输出

## 核心函数

### 1. PID控制器初始化
```c
PID_Init(&yaw_pid, 2.0f, 0.1f, 0.5f, 300.0f, 100.0f);
```
- Kp=2.0：比例系数，控制响应速度
- Ki=0.1：积分系数，消除稳态误差
- Kd=0.5：微分系数，减少超调
- 最大输出=300：限制电机最大速度
- 积分限幅=100：防止积分饱和

### 2. 旋转控制函数
```c
StartRotation(1);    // 右转90°
StartRotation(2);    // 左转90°
StartRotationAngle(45.0f);  // 自定义角度旋转
StopRotation();      // 停止旋转
IsRotationComplete(); // 检查是否完成
```

### 3. 状态枚举
- `ROTATION_IDLE`：空闲状态
- `ROTATION_TURNING`：正在旋转
- `ROTATION_COMPLETE`：旋转完成

## 工作原理

### 1. 角度获取
- 使用MPU6050的DMP功能获取融合后的Yaw角度
- 角度范围：-180° 到 +180°
- 更新频率：200Hz（DMP内部处理）

### 2. PID控制循环
- 在1ms定时器中断中执行PID计算
- 计算角度误差：`error = target - current`
- 处理角度循环问题（-180°/+180°边界）
- 输出电机控制信号

### 3. 电机控制
- 左右轮反向旋转实现原地旋转
- 电机速度限制在±200范围内
- 使用control_speed函数控制电机

## 参数调整指南

### PID参数调整
1. **Kp（比例系数）**：
   - 过小：响应慢，到达时间长
   - 过大：震荡，超调严重
   - 建议范围：1.0-3.0

2. **Ki（积分系数）**：
   - 消除稳态误差
   - 过大：系统不稳定
   - 建议范围：0.05-0.2

3. **Kd（微分系数）**：
   - 减少超调，提高稳定性
   - 过大：对噪声敏感
   - 建议范围：0.1-1.0

### 电机参数调整
- 最大速度：根据电机特性调整（建议100-300）
- 死区补偿：在电机驱动中已设置
- 速度限制：防止电机过载

## 测试模式

### 模式1：组合测试（test_mode = 0）
- 前进 → 右转90° → 前进 → 左转90° → 停止
- 适合测试完整的移动序列

### 模式2：纯旋转测试（test_mode = 1）
- 右转90° → 等待2秒 → 左转90° → 等待3秒 → 循环
- 适合调试PID参数

## OLED显示信息
- 第2行：当前Yaw角度
- 第3行：目标Yaw角度
- 第4行：PID输出值
- 右侧：旋转状态（IDLE/TURN/DONE）

## 故障排除

### 1. 旋转不准确
- 检查PID参数是否合适
- 确认MPU6050校准是否正确
- 检查电机死区设置

### 2. 旋转震荡
- 降低Kp值
- 增加Kd值
- 检查机械结构是否稳定

### 3. 旋转超调
- 降低Kp值
- 增加Kd值
- 降低最大输出限制

### 4. 响应慢
- 增加Kp值
- 检查定时器中断是否正常
- 确认电机速度是否足够

## 注意事项
1. MPU6050需要预热，建议上电后等待几秒再开始旋转
2. 偏航角存在漂移是正常现象，短时间内影响不大
3. 地面摩擦力会影响旋转精度，建议在平滑地面测试
4. 电池电压低时会影响电机性能，注意电池电量

## 扩展功能
- 可以修改`StartRotationAngle()`函数实现任意角度旋转
- 可以添加速度控制，实现变速旋转
- 可以添加加速度控制，实现平滑启停
