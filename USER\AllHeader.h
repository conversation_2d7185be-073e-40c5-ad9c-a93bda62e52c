/**
* @par Copyright (C): 2016-2026, Shenzhen Yahboom Tech
* @file         // ALLHeader.h
* <AUTHOR> lly
* @version      // V1.0
* @date         // 240628
* @brief        // ������е�ͷ�ļ�
* @details      
* @par History  //
*               
*/


#ifndef __ALLHEADER_H
#define __ALLHEADER_H


//ͷ�ļ�
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <stdbool.h>
#include <stdint.h>

extern uint8_t times;

#include "stm32f10x.h"
#include "stm32f10x_gpio.h"

#include "myenum.h"

#include "delay.h"
#include "bsp.h"
#include "usart.h"
#include "IOI2C.h"
#include "bsp_motor_iic.h"

#include "bsp_timer.h"
#include "OLED.h"
#include "sys.h"
#include "MPU6050_I2C.h"
#include "inv_mpu.h"
#include "inv_mpu_dmp_motion_driver.h"
#include "mpu6050.h"

// 函数声明
void Car_Move(void);
void StartRotation(uint8_t direction);
void StartRotationAngle(float angle);
void StopRotation(void);
uint8_t IsRotationComplete(void);
void TestRotation(void);

#endif


