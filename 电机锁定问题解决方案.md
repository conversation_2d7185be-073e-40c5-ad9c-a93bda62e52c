# 电机锁定问题解决方案

## 🔍 问题分析

### 电机锁定的原因
1. **控制信号未清零**: 停止时仍有PWM信号输出
2. **电机驱动板特性**: 某些驱动板在接收到信号时会锁定电机
3. **PID输出残留**: 停止时PID控制器仍有输出
4. **状态切换不完整**: 旋转状态未正确切换到IDLE

## 🛠️ 已实施的解决方案

### 1. 改进的停止逻辑
在所有可能停止的地方都确保发送零速度信号：

```c
// 在ProcessRotationControl函数中
if (rotation_state != ROTATION_TURNING) {
    control_speed(0, 0, 0, 0);  // 确保电机停止
    last_motor_speed = 0.0f;
    stable_counter = 0;
    return;
}

// 到达目标时
if (stable_counter >= 50) {
    rotation_state = ROTATION_COMPLETE;
    control_speed(0, 0, 0, 0);  // 确保停止
    last_motor_speed = 0.0f;
    return;
}

// 超时时
if (rotation_timeout == 0) {
    rotation_state = ROTATION_COMPLETE;
    control_speed(0, 0, 0, 0);  // 确保停止
    return;
}
```

### 2. 状态重置机制
在StartRotation函数中添加预停止：

```c
void StartRotation(uint8_t direction) {
    // 确保电机先停止
    control_speed(0, 0, 0, 0);
    delay_ms(100);  // 短暂延时确保电机停止
    
    // ... 其他初始化代码
}
```

### 3. 死区处理改进
避免小信号导致的电机抖动：

```c
// 死区处理：小于阈值时直接设为0
if (fabs(motor_speed) < 30) {
    motor_speed = 0;
} else {
    // 死区补偿
    if (motor_speed > 0 && motor_speed < 40) motor_speed = 40;
    if (motor_speed < 0 && motor_speed > -40) motor_speed = -40;
}
```

## 🔧 额外的解决方案

### 方案1: 添加强制停止函数

在main.c中添加以下函数：

```c
// 强制停止所有电机
void ForceStopMotors(void) {
    control_speed(0, 0, 0, 0);
    delay_ms(10);
    control_pwm(0, 0, 0, 0);  // 同时使用PWM控制确保停止
    delay_ms(10);
    control_speed(0, 0, 0, 0);
}

// 在旋转完成时调用
void CompleteRotation(void) {
    rotation_state = ROTATION_COMPLETE;
    ForceStopMotors();
    
    // 重置PID状态
    yaw_pid.integral = 0.0f;
    yaw_pid.last_error = 0.0f;
    yaw_pid.output = 0.0f;
}
```

### 方案2: 定期状态检查

在主循环中添加状态检查：

```c
// 在main函数的while循环中添加
static uint32_t last_check_time = 0;
uint32_t current_time = TIM1_GetCounter();

if ((current_time - last_check_time) > 1000) {  // 每秒检查一次
    if (rotation_state == ROTATION_COMPLETE) {
        control_speed(0, 0, 0, 0);  // 确保电机停止
    }
    last_check_time = current_time;
}
```

### 方案3: 电机驱动板复位

如果问题仍然存在，可能需要复位电机驱动板：

```c
// 电机驱动板软复位函数
void ResetMotorDriver(void) {
    // 发送停止信号
    control_speed(0, 0, 0, 0);
    delay_ms(100);
    
    // 重新初始化电机参数
    Set_motor_type(1);
    delay_ms(50);
    Set_Pluse_Phase(40);
    delay_ms(50);
    Set_Pluse_line(11);
    delay_ms(50);
    Set_Wheel_dis(67.00);
    delay_ms(50);
    Set_motor_deadzone(1900);
    delay_ms(50);
    
    // 再次确保停止
    control_speed(0, 0, 0, 0);
}
```

## 🧪 测试验证方法

### 测试1: 停止状态验证
```c
void TestMotorStop(void) {
    // 启动旋转
    StartRotation(1);
    delay_ms(2000);
    
    // 强制停止
    StopRotation();
    delay_ms(1000);
    
    // 检查电机是否可以手动转动
    OLED_ShowString(1, 1, "Check Motor");
    delay_ms(5000);  // 给用户时间检查
}
```

### 测试2: 状态切换验证
```c
void TestStateTransition(void) {
    for (int i = 0; i < 5; i++) {
        StartRotation(1);
        while (!IsRotationComplete()) {
            delay_ms(10);
        }
        
        // 检查状态
        if (rotation_state == ROTATION_COMPLETE) {
            OLED_ShowString(1, 1, "State OK");
        } else {
            OLED_ShowString(1, 1, "State ERR");
        }
        
        delay_ms(2000);
    }
}
```

## 📋 问题排查清单

### ✅ 软件检查
- [ ] 所有停止位置都调用control_speed(0,0,0,0)
- [ ] 旋转状态正确切换到COMPLETE或IDLE
- [ ] PID输出在停止时为0
- [ ] 没有定时器中断继续发送控制信号

### ✅ 硬件检查
- [ ] 电机驱动板连接正常
- [ ] 电源电压稳定
- [ ] 电机本身没有机械卡死
- [ ] 驱动板没有过热保护激活

### ✅ 参数检查
- [ ] 电机死区设置合理(1900)
- [ ] 电机类型设置正确(L型520电机)
- [ ] 减速比和磁环线数正确

## 🚨 紧急处理方法

如果电机仍然锁定，可以尝试以下紧急处理：

### 方法1: 断电重启
1. 断开电源
2. 等待5秒
3. 重新上电
4. 重新初始化系统

### 方法2: 手动解锁
1. 在代码中添加解锁函数：
```c
void UnlockMotors(void) {
    // 发送反向小信号
    control_speed(0, 10, 0, -10);
    delay_ms(100);
    control_speed(0, -10, 0, 10);
    delay_ms(100);
    control_speed(0, 0, 0, 0);
}
```

### 方法3: 驱动板复位
1. 重新初始化IIC通信
2. 重新设置所有电机参数
3. 发送停止命令

## 📞 技术支持

如果问题仍然存在，请记录以下信息：
1. 电机锁定发生的具体时机
2. OLED显示的状态信息
3. 是否所有电机都锁定
4. 断电重启后是否恢复正常
5. 使用的电机驱动板型号
